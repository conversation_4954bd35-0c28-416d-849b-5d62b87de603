{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "Quartz": "Information"}}, "ClassificationJob": {"Enabled": true, "IntervalMinutes": 10, "RunOnStartup": true, "TimeoutMinutes": 120, "DailyExecutionTime": null}, "RecommendationService": {"BaseUrl": "http://recommendation-service:80", "AnalysisEndpoint": "/api/Recomendacao/iniciar", "HealthCheckEndpoint": "/health", "TimeoutSeconds": 30}}