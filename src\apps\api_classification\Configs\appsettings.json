{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*", "Urls": "http://localhost:8080", "ConnectionStrings": {"DefaultConnection": "Host=localhost;Database=mean_girls;Username=inteli;Password=**********;"}, "ServiceConfiguration": {"BaseUrl": "http://localhost:8080", "ServiceName": "Pati.ClassificationService", "Version": "1.0.0"}, "SVDModel": {"ModelPath": "/app/models/modelo_svd_treinado.pkl", "MinSuitabilityThreshold": 0.5, "DefaultRecommendationCount": 5, "EnableModelCaching": true, "PythonServiceUrl": "http://localhost:8500"}, "RecommendationService": {"BaseUrl": "http://localhost:18502", "AnalysisEndpoint": "/api/Recomendacao/iniciar", "HealthCheckEndpoint": "/health", "TimeoutSeconds": 30, "RetryAttempts": 3}, "ClassificationJob": {"Enabled": true, "IntervalMinutes": 10, "RunOnStartup": true, "TimeoutMinutes": 60, "DailyExecutionTime": null}, "DataFiles": {"InconformidadesPath": "src/database/inconformidades.csv", "TipoAtivosPath": "src/database/Tipo_ativos_com_risco.csv"}}