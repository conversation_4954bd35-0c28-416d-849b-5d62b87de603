{"ConnectionStrings": {"PostgreSQL": "Host=db;Database=mean_girls;Username=inteli;Password=**********", "MongoDB": "***************************************************************************"}, "MongoDbSettings": {"DatabaseName": "mean_girls_mongo", "CarteirasCollectionName": "Carteiras Classificadas", "RecomendacoesCollectionName": "recommendations"}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}, "Console": {"LogLevel": {"Default": "Information"}, "FormatterName": "Simple", "FormatterOptions": {"SingleLine": false, "IncludeScopes": true, "TimestampFormat": "yyyy-MM-dd HH:mm:ss "}}}, "AllowedHosts": "*"}