using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using ApiMlRecommender.DTOs;
using Microsoft.ML;
using Microsoft.ML.Data;
using Microsoft.ML.Trainers;

namespace ApiMlRecommender.Services
{
    public class RecomendacaoService : IRecomendacaoService
    {
        private readonly PostgreSqlService _postgreSqlService;
        private readonly MongoDbService _mongoDbService;
        private List<RecomendacaoRequestDto> _dadosInconformidades;
        private readonly MLContext _mlContext;
        private ITransformer? _model;
        private PredictionEngine<InvestmentEntry, InvestmentPrediction>? _predictionEngine;

        public RecomendacaoService(PostgreSqlService postgreSqlService, MongoDbService mongoDbService)
        {
            _postgreSqlService = postgreSqlService;
            _mongoDbService = mongoDbService;
            _dadosInconformidades = new List<RecomendacaoRequestDto>();
            _mlContext = new MLContext(seed: 42);
        }

        private class InvestmentEntry
        {
            public uint ContaId { get; set; }
            public uint NomeAtivoId { get; set; }
            public float Label { get; set; }
        }

        private class InvestmentPrediction
        {
            public float Score { get; set; }
        }

        private readonly Dictionary<string, (decimal RendaFixa, decimal RendaVariavel)> _proporcoesIdeais = new()
        {
            { "CONSERVADOR", (RendaFixa: 0.9m, RendaVariavel: 0.1m) },
            { "MODERADO", (RendaFixa: 0.6m, RendaVariavel: 0.4m) },
            { "ARROJADO", (RendaFixa: 0.3m, RendaVariavel: 0.7m) },
            { "SOFISTICADO", (RendaFixa: 0.2m, RendaVariavel: 0.8m) }
        };

        private readonly Dictionary<string, List<string>> _regrasPerfil = new()
        {
            { "CONSERVADOR", new List<string> { "CONSERVADOR" } },
            { "MODERADO", new List<string> { "CONSERVADOR", "MODERADO" } },
            { "ARROJADO", new List<string> { "CONSERVADOR", "MODERADO", "ARROJADO" } },
            { "SOFISTICADO", new List<string> { "CONSERVADOR", "MODERADO", "ARROJADO", "SOFISTICADO" } }
        };

        private async Task CarregarDadosSeNecessarioAsync()
        {
            if (!_dadosInconformidades.Any())
            {
                _dadosInconformidades = await _postgreSqlService.GetCarteirasInconformesAsync();
                TreinarModeloSVD();
            }
        }

        private void TreinarModeloSVD()
        {
            try
            {
                var entries = _dadosInconformidades
                    .SelectMany(cliente => cliente.Investimentos.Select(inv => new InvestmentEntry
                    {
                        ContaId = (uint)Math.Abs(cliente.Cpf.GetHashCode()),
                        NomeAtivoId = (uint)Math.Abs(inv.Codigo.GetHashCode()),
                        Label = (float)inv.ValorAtual
                    }))
                    .ToList();

                if (!entries.Any())
                {
                    throw new InvalidOperationException("Não há dados suficientes para treinar o modelo");
                }

                var data = _mlContext.Data.LoadFromEnumerable(entries);

                var pipeline = _mlContext.Transforms.Conversion.MapValueToKey("userIdEncoded", nameof(InvestmentEntry.ContaId))
                    .Append(_mlContext.Transforms.Conversion.MapValueToKey("itemIdEncoded", nameof(InvestmentEntry.NomeAtivoId)))
                    .Append(_mlContext.Recommendation().Trainers.MatrixFactorization(
                        labelColumnName: "Label",
                        matrixColumnIndexColumnName: "userIdEncoded",
                        matrixRowIndexColumnName: "itemIdEncoded",
                        numberOfIterations: 20,
                        approximationRank: 32,
                        learningRate: 0.1));

                _model = pipeline.Fit(data);
                _predictionEngine = _mlContext.Model.CreatePredictionEngine<InvestmentEntry, InvestmentPrediction>(_model);
            }
            catch (Exception ex)
            {
                throw new Exception($"Erro ao treinar modelo SVD: {ex.Message}", ex);
            }
        }

        private bool RiscoPermitido(string perfil, string risco)
        {
            var perfilUpper = perfil?.ToUpper() ?? string.Empty;
            var riscoUpper = risco?.ToUpper() ?? string.Empty;
            return _regrasPerfil.TryGetValue(perfilUpper, out var riscosPermitidos) && 
                   riscosPermitidos.Contains(riscoUpper);
        }

        public async Task<RecomendacaoResponseDto> GerarRecomendacaoAsync(RecomendacaoRequestDto request)
        {
            if (request == null || string.IsNullOrEmpty(request.Cpf) || !request.Investimentos.Any() || request.ComplianceStatus?.ToLower() != "inconforme")
            {
                throw new ArgumentException("Requisição inválida ou carteira não inconforme.");
            }

            await CarregarDadosSeNecessarioAsync();

            var scoreAdequacao = CalcularScoreAdequacao(request.Investimentos, request.PerfilRisco);
            var statusConformidade = DeterminarStatusConformidade(scoreAdequacao);
            var investimentosAnalisados = AnalisarInvestimentos(request.Investimentos, request.PerfilRisco);
            var recomendacoesAjuste = await GerarRecomendacoesSVDAsync(request);
            var resumo = CriarResumoRecomendacao(recomendacoesAjuste, investimentosAnalisados, request);

            var recomendacaoResponse = new RecomendacaoResponseDto
            {
                Cpf = request.Cpf,
                Nome = request.Nome,
                PerfilRisco = request.PerfilRisco,
                DataRecomendacao = DateTime.Now,
                StatusConformidade = statusConformidade,
                ScoreAdequacao = scoreAdequacao,
                ValorTotalCarteira = request.Investimentos.Sum(i => i.ValorAtual),
                InvestimentosAtuais = investimentosAnalisados,
                RecomendacoesAjuste = recomendacoesAjuste,
                Resumo = resumo
            };

            // Salvar no MongoDB
            var recomendacaoMongo = new RecomendacaoInvestimento
            {
                ClientId = request.Cpf,
                Investments = recomendacoesAjuste
                    .Where(r => r.TipoAcao == "Comprar" || r.TipoAcao == "Aumentar")
                    .Select(r => new InvestmentRecommendation
                    {
                        InvestmentId = r.CodigoInvestimento,
                        Score = (double)(r.PercentualRecomendado / 100)
                    })
                    .ToList()
            };
            
            var timestamp_saving = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
            Console.WriteLine($"[{timestamp_saving}] 💾 SALVANDO: Tentando salvar recomendação para o cliente ID {request.Cpf} no MongoDB.");
            Console.WriteLine($"[{timestamp_saving}] 📊 DEBUG: Total de recomendações geradas: {recomendacoesAjuste.Count}");
            Console.WriteLine($"[{timestamp_saving}] 📊 DEBUG: Recomendações de compra/aumento: {recomendacaoMongo.Investments.Count}");
            Console.WriteLine($"[{timestamp_saving}] 📊 DEBUG: Tipos de ação encontrados: {string.Join(", ", recomendacoesAjuste.Select(r => r.TipoAcao).Distinct())}");
            
            await _mongoDbService.SaveRecomendacaoAsync(recomendacaoMongo);

            var timestamp_saved = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
            Console.WriteLine($"[{timestamp_saved}] 💾 SALVO: Recomendação para o cliente ID {request.Cpf} salva no MongoDB.");

            return recomendacaoResponse;
        }

        public async Task IniciarRecomendacoesAsync()
        {
            var timestamp_start = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
            Console.WriteLine($"[{timestamp_start}] 🚀 INÍCIO: Iniciando processo de geração de recomendações.");
            
            Console.WriteLine($"[{DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")}] 📊 BUSCA: Buscando clientes inconformes no PostgreSQL.");
            var carteirasInconformes = await _postgreSqlService.GetCarteirasInconformesAsync();
            Console.WriteLine($"[{DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")}] 📊 ENCONTRADOS: {carteirasInconformes.Count} clientes inconformes encontrados.");

            foreach (var carteira in carteirasInconformes)
            {
                var timestamp_loop_start = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
                Console.WriteLine($"[{timestamp_loop_start}] 👤 PROCESSANDO: Iniciando geração de recomendação para o cliente ID: {carteira.Cpf}.");
                try
                {
                    await GerarRecomendacaoAsync(carteira);
                    var timestamp_loop_success = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
                    Console.WriteLine($"[{timestamp_loop_success}] ✅ SUCESSO: Recomendação para o cliente ID {carteira.Cpf} gerada e salva com sucesso.");
                }
                catch (Exception ex)
                {
                    var timestamp_loop_error = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
                    Console.WriteLine($"[{timestamp_loop_error}] ❌ ERRO: Falha ao gerar recomendação para o cliente ID {carteira.Cpf}. Detalhes: {ex.Message}");
                }
            }
            var timestamp_end = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
            Console.WriteLine($"[{timestamp_end}] ✅ TÉRMINO: Processo de geração de recomendações finalizado.");
        }

        private async Task<List<RecomendacaoAjusteDto>> GerarRecomendacoesSVDAsync(RecomendacaoRequestDto request)
        {
            await CarregarDadosSeNecessarioAsync();
            if (_predictionEngine == null)
                throw new InvalidOperationException("Modelo SVD não treinado.");

            var recomendacoes = new List<RecomendacaoAjusteDto>();
            var perfilCarteira = request.PerfilRisco.ToUpper();

            Console.WriteLine($"[{DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")}] 🔍 DEBUG: Gerando recomendações para cliente {request.Cpf} com {request.Investimentos.Count} investimentos");

            var ajustesRebalanceamento = GerarAjustesRebalanceamento(request);
            Console.WriteLine($"[{DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")}] 🔍 DEBUG: Ajustes de rebalanceamento gerados: {ajustesRebalanceamento.Count}");
            recomendacoes.AddRange(ajustesRebalanceamento);

            var novasRecomendacoes = await GerarRecomendacoesNovosAtivosAsync(request);
            Console.WriteLine($"[{DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")}] 🔍 DEBUG: Novas recomendações geradas: {novasRecomendacoes.Count}");
            recomendacoes.AddRange(novasRecomendacoes);

            Console.WriteLine($"[{DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")}] 🔍 DEBUG: Total de recomendações: {recomendacoes.Count}");

            return recomendacoes.OrderBy(r => r.Prioridade).ThenByDescending(r => r.ValorRecomendado).ToList();
        }

        private List<RecomendacaoAjusteDto> GerarAjustesRebalanceamento(RecomendacaoRequestDto request)
        {
            var ajustes = new List<RecomendacaoAjusteDto>();
            var perfilUpper = request.PerfilRisco.ToUpper();

            foreach (var investimento in request.Investimentos)
            {
                var categoriaUpper = investimento.Categoria.ToUpper();
                var percentualIdeal = CalcularPercentualIdeal(categoriaUpper, perfilUpper);
                var diferenca = investimento.PercentualCarteira - percentualIdeal;

                if (Math.Abs(diferenca) > 10)
                {
                    var tipoAcao = diferenca > 0 ? "Reduzir" : "Aumentar";
                    var valorAjuste = Math.Abs(diferenca / 100) * investimento.ValorAtual;
                    var prioridade = Math.Abs(diferenca) > 20 ? 1 : 2;

                    ajustes.Add(new RecomendacaoAjusteDto
                    {
                        TipoAcao = tipoAcao,
                        CodigoInvestimento = investimento.Codigo,
                        NomeInvestimento = investimento.Nome,
                        ValorRecomendado = valorAjuste,
                        PercentualRecomendado = percentualIdeal,
                        Prioridade = prioridade,
                        Justificativa = $"{tipoAcao} exposição em {Math.Abs(diferenca):F1}% para adequar ao perfil."
                    });
                }
            }

            return ajustes;
        }

        private async Task<List<RecomendacaoAjusteDto>> GerarRecomendacoesNovosAtivosAsync(RecomendacaoRequestDto request)
        {
            var recomendacoes = new List<RecomendacaoAjusteDto>();
            var clienteId = request.Cpf;
            var perfilCarteira = request.PerfilRisco.ToUpper();

            var ativosCliente = request.Investimentos.Select(i => i.Codigo).ToHashSet();
            var todosAtivos = _dadosInconformidades
                .SelectMany(c => c.Investimentos)
                .Where(i => !ativosCliente.Contains(i.Codigo))
                .GroupBy(i => i.Codigo)
                .Select(g => g.First())
                .ToList();

            Console.WriteLine($"[{DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")}] 🔍 DEBUG: Ativos do cliente: {ativosCliente.Count}");
            Console.WriteLine($"[{DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")}] 🔍 DEBUG: Total de ativos disponíveis: {todosAtivos.Count}");

            var proporcaoIdeal = _proporcoesIdeais.GetValueOrDefault(perfilCarteira, (RendaFixa: 0.5m, RendaVariavel: 0.5m));
            var precisaMaisRendaFixa = request.Investimentos
                .Where(i => i.Categoria.ToUpper() == "RENDA FIXA")
                .Sum(i => i.ValorAtual) / request.Investimentos.Sum(i => i.ValorAtual) < proporcaoIdeal.RendaFixa;

            var maxRecomendacoes = 5;
            var recomendacoesPorTipo = new List<(InvestimentoDto Ativo, float Score)>();

            foreach (var ativo in todosAtivos.Take(50))
            {
                if (!RiscoPermitido(perfilCarteira, ativo.Risco))
                    continue;

                if (ativo.Categoria.ToUpper() == "RENDA FIXA" && !precisaMaisRendaFixa)
                    continue;

                var entry = new InvestmentEntry
                {
                    ContaId = (uint)Math.Abs(clienteId.GetHashCode()),
                    NomeAtivoId = (uint)Math.Abs(ativo.Codigo.GetHashCode()),
                    Label = 0f
                };

                var predicao = _predictionEngine.Predict(entry);
                recomendacoesPorTipo.Add((ativo, Math.Max(0, predicao.Score)));
            }

            Console.WriteLine($"[{DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")}] 🔍 DEBUG: Ativos que passaram pelos filtros: {recomendacoesPorTipo.Count}");

            var topRecomendacoes = recomendacoesPorTipo
                .OrderByDescending(x => x.Score)
                .Take(maxRecomendacoes)
                .ToList();

            Console.WriteLine($"[{DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")}] 🔍 DEBUG: Top recomendações selecionadas: {topRecomendacoes.Count}");

            var valorDisponivel = request.ValorDisponivel ?? 10000m;
            var valorPorAtivo = topRecomendacoes.Any() ? valorDisponivel / topRecomendacoes.Count : 0m;

            foreach (var (ativo, score) in topRecomendacoes)
            {
                recomendacoes.Add(new RecomendacaoAjusteDto
                {
                    TipoAcao = "Comprar",
                    CodigoInvestimento = ativo.Codigo,
                    NomeInvestimento = ativo.Nome,
                    ValorRecomendado = valorPorAtivo,
                    PercentualRecomendado = Math.Round((valorPorAtivo / (request.Investimentos.Sum(i => i.ValorAtual) + valorDisponivel)) * 100, 2),
                    Prioridade = score > 0.3f ? 1 : 2,
                    Justificativa = $"Recomendado pelo modelo SVD (Score: {score:F2})."
                });
            }

            return recomendacoes;
        }

        public decimal CalcularScoreAdequacao(List<InvestimentoDto> investimentos, string perfilRisco)
        {
            if (!investimentos.Any()) return 0;

            var scoreTotal = 0m;
            var pesoTotal = 0m;

            foreach (var investimento in investimentos)
            {
                var peso = investimento.ValorAtual;
                var scoreRisco = CalcularScoreRisco(investimento.NivelRisco, perfilRisco);
                scoreTotal += scoreRisco * peso;
                pesoTotal += peso;
            }

            var scoreBase = pesoTotal > 0 ? scoreTotal / pesoTotal : 0;
            var penalizacaoConcentracao = CalcularPenalizacaoConcentracao(investimentos);
            var penalizacaoAlocacao = CalcularPenalizacaoAlocacao(investimentos, perfilRisco);

            return Math.Max(0, Math.Round(scoreBase - penalizacaoConcentracao - penalizacaoAlocacao, 1));
        }

        private decimal CalcularScoreRisco(int nivelRiscoAtivo, string perfilCliente)
        {
            var nivelPerfilCliente = perfilCliente.ToUpper() switch
            {
                "CONSERVADOR" => 1,
                "MODERADO" => 3,
                "ARROJADO" => 4,
                "SOFISTICADO" => 5,
                _ => 2
            };

            var diferenca = Math.Abs(nivelRiscoAtivo - nivelPerfilCliente);
            return Math.Max(0, 100m - (diferenca * 25m));
        }

        private decimal CalcularPenalizacaoConcentracao(List<InvestimentoDto> investimentos)
        {
            var penalizacao = 0m;
            var maiorValor = investimentos.Max(i => i.ValorAtual);
            var totalInvestimentos = investimentos.Sum(i => i.ValorAtual);
            var proporcaoMaior = totalInvestimentos > 0 ? (maiorValor / totalInvestimentos) * 100 : 0;

            if (proporcaoMaior > 50) penalizacao += (proporcaoMaior - 50) * 0.8m;
            else if (proporcaoMaior > 30) penalizacao += (proporcaoMaior - 30) * 0.3m;

            if (investimentos.Count < 3) penalizacao += 15m;
            else if (investimentos.Count < 5) penalizacao += 5m;

            return Math.Min(penalizacao, 40m);
        }

        private decimal CalcularPenalizacaoAlocacao(List<InvestimentoDto> investimentos, string perfilRisco)
        {
            var perfilUpper = perfilRisco.ToUpper();
            var proporcaoIdeal = _proporcoesIdeais.GetValueOrDefault(perfilUpper, (RendaFixa: 0.5m, RendaVariavel: 0.5m));

            var totalRF = investimentos.Where(i => i.Categoria.ToUpper() == "RENDA FIXA").Sum(i => i.ValorAtual);
            var totalRV = investimentos.Where(i => i.Categoria.ToUpper() == "RENDA VARIÁVEL").Sum(i => i.ValorAtual);
            var total = totalRF + totalRV;

            var proporcaoRFAtual = total > 0 ? (totalRF / total) * 100 : 0;
            var proporcaoRVAtual = total > 0 ? (totalRV / total) * 100 : 0;

            var diferencaRF = Math.Abs(proporcaoRFAtual - (proporcaoIdeal.RendaFixa * 100));
            var diferencaRV = Math.Abs(proporcaoRVAtual - (proporcaoIdeal.RendaVariavel * 100));

            return Math.Min((diferencaRF + diferencaRV) * 0.3m, 25m);
        }

        private string DeterminarStatusConformidade(decimal score)
        {
            return score switch
            {
                >= 85m => "Adequado",
                >= 70m => "LevementeDesbalanceado",
                >= 50m => "Desbalanceado",
                _ => "DesbalanceadoSevero"
            };
        }

        private List<InvestimentoAnalisadoDto> AnalisarInvestimentos(List<InvestimentoDto> investimentos, string perfilRisco)
        {
            var resultado = new List<InvestimentoAnalisadoDto>();

            foreach (var investimento in investimentos)
            {
                var categoriaUpper = investimento.Categoria.ToUpper();
                var perfilUpper = perfilRisco.ToUpper();
                var percentualIdeal = CalcularPercentualIdeal(categoriaUpper, perfilUpper);
                var diferenca = investimento.PercentualCarteira - percentualIdeal;

                resultado.Add(new InvestimentoAnalisadoDto
                {
                    Codigo = investimento.Codigo,
                    Nome = investimento.Nome,
                    Categoria = investimento.Categoria,
                    Subcategoria = investimento.Subcategoria ?? string.Empty,
                    TipoAtivo = investimento.TipoAtivo,
                    NivelRisco = investimento.NivelRisco,
                    Risco = investimento.Risco,
                    ValorAtual = investimento.ValorAtual,
                    PercentualCarteira = investimento.PercentualCarteira,
                    RentabilidadeEsperada = investimento.RentabilidadeEsperada ?? 0,
                    EstaAdequado = Math.Abs(diferenca) <= 10 && RiscoPermitido(perfilUpper, investimento.Risco),
                    PercentualIdeal = percentualIdeal,
                    DiferencaPercentual = diferenca,
                    Observacoes = GerarObservacoes(diferenca, investimento.NivelRisco, perfilRisco, investimento.Risco)
                });
            }

            return resultado;
        }

        private decimal CalcularPercentualIdeal(string categoria, string perfilRisco)
        {
            return (categoria.ToUpper(), perfilRisco.ToUpper()) switch
            {
                ("RENDA FIXA", "CONSERVADOR") => 90m,
                ("RENDA FIXA", "MODERADO") => 60m,
                ("RENDA FIXA", "ARROJADO") => 30m,
                ("RENDA FIXA", "SOFISTICADO") => 20m,
                ("RENDA VARIÁVEL", "CONSERVADOR") => 10m,
                ("RENDA VARIÁVEL", "MODERADO") => 40m,
                ("RENDA VARIÁVEL", "ARROJADO") => 70m,
                ("RENDA VARIÁVEL", "SOFISTICADO") => 80m,
                _ => 25m
            };
        }

        private string GerarObservacoes(decimal diferenca, int nivelRisco, string perfilRisco, string riscoAtivo)
        {
            var observacoes = new List<string>();

            if (Math.Abs(diferenca) > 20)
                observacoes.Add($"Recomenda-se {(diferenca > 0 ? "reduzir" : "aumentar")} exposição em {Math.Abs(diferenca):F1}%");
            else if (Math.Abs(diferenca) > 10)
                observacoes.Add($"Leve desbalanceamento de {Math.Abs(diferenca):F1}%");

            if (CalcularScoreRisco(nivelRisco, perfilRisco) < 60)
                observacoes.Add("Risco incompatível com o perfil do investidor");

            if (!RiscoPermitido(perfilRisco.ToUpper(), riscoAtivo.ToUpper()))
                observacoes.Add("Ativo não adequado para o perfil regulatório");

            return observacoes.Any() ? string.Join("; ", observacoes) : "Investimento adequado";
        }

        private ResumoRecomendacaoDto CriarResumoRecomendacao(
            List<RecomendacaoAjusteDto> recomendacoes,
            List<InvestimentoAnalisadoDto> investimentosAnalisados,
            RecomendacaoRequestDto request)
        {
            var totalAjustes = recomendacoes.Count;
            var ajustesAltaPrioridade = recomendacoes.Count(r => r.Prioridade == 1);
            var valorTotalMovimentacao = recomendacoes.Sum(r => r.ValorRecomendado);

            var beneficios = new List<string>();
            var riscos = new List<string>();

            if (ajustesAltaPrioridade > 0) beneficios.Add("Melhoria significativa na adequação regulatória");
            if (recomendacoes.Any(r => r.TipoAcao == "Comprar")) beneficios.Add("Maior diversificação da carteira");
            beneficios.Add("Redução do risco de não conformidade com CVM 175");
            if (totalAjustes > 0) beneficios.Add("Otimização da relação risco-retorno");

            if (valorTotalMovimentacao > 50000) riscos.Add("Alto volume de movimentação pode gerar custos operacionais");
            if (ajustesAltaPrioridade > 3) riscos.Add("Múltiplos ajustes simultâneos podem impactar liquidez");
            riscos.Add("Necessidade de acompanhamento contínuo após implementação");
            if (recomendacoes.Any(r => r.TipoAcao == "Reduzir")) riscos.Add("Possíveis custos de saída");

            var totalInvestimentos = investimentosAnalisados.Sum(i => i.ValorAtual);
            var valorRendaFixa = investimentosAnalisados.Where(i => i.Categoria.ToUpper() == "RENDA FIXA").Sum(i => i.ValorAtual);
            var valorRendaVariavel = investimentosAnalisados.Where(i => i.Categoria.ToUpper() == "RENDA VARIÁVEL").Sum(i => i.ValorAtual);

            var proporcaoRendaFixaAtual = totalInvestimentos > 0 ? (valorRendaFixa / totalInvestimentos) * 100 : 0m;
            var proporcaoRendaVariavelAtual = totalInvestimentos > 0 ? (valorRendaVariavel / totalInvestimentos) * 100 : 0m;

            var perfilUpper = request.PerfilRisco.ToUpper();
            var proporcaoIdeal = _proporcoesIdeais.GetValueOrDefault(perfilUpper, (RendaFixa: 0.5m, RendaVariavel: 0.5m));

            return new ResumoRecomendacaoDto
            {
                TotalAjustes = totalAjustes,
                AjustesAltaPrioridade = ajustesAltaPrioridade,
                ValorTotalMovimentacao = valorTotalMovimentacao,
                TempoEstimadoImplementacao = totalAjustes switch
                {
                    0 => "Não necessário",
                    <= 2 => "1-2 semanas",
                    <= 5 => "2-4 semanas",
                    _ => "4-6 semanas"
                },
                BeneficiosEsperados = beneficios,
                RiscosIdentificados = riscos,
                ObservacoesGerais = $"Análise baseada na CVM 175 para perfil {request.PerfilRisco}. {(totalAjustes == 0 ? "Carteira balanceada." : $"Implementar {totalAjustes} ajustes.")}",
                ProporcaoRendaFixaAtual = proporcaoRendaFixaAtual,
                ProporcaoRendaVariavelAtual = proporcaoRendaVariavelAtual,
                ProporcaoRendaFixaIdeal = proporcaoIdeal.RendaFixa * 100,
                ProporcaoRendaVariavelIdeal = proporcaoIdeal.RendaVariavel * 100,
                PerfilInvestimentos = request.PerfilInvestimentos ?? string.Empty
            };
        }
    }
}