-- <PERSON><PERSON><PERSON> das tabelas

-- Tabela ADVISOR (Assessor)
CREATE TABLE ADVISOR (
    advisor_id SERIAL PRIMARY KEY,
    uid VARCHAR(128) UNIQUE NOT NULL,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT check_updated_at CHECK (updated_at >= created_at)
);

-- Tabela CLIENT (Cliente)
CREATE TABLE CLIENT (
    client_id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    phone_number VARCHAR(20),
    email VARCHAR(100) UNIQUE NOT NULL,
    risk_profile_wallet VARCHAR(50) NOT NULL,
    risk_profile_form VARCHAR(50) NOT NULL,
    compliance BOOLEAN NOT NULL,
    advisor_id INTEGER NOT NULL,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    <PERSON><PERSON><PERSON><PERSON><PERSON> KEY (advisor_id) REFERENCES ADVISOR(advisor_id) ON DELETE RESTRICT,
    CONSTRAINT check_updated_at CHECK (updated_at >= created_at)
);

-- Tabela INVESTMENT (Investimento)
CREATE TABLE INVESTMENT (
    investment_id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    type VARCHAR(50) NOT NULL,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT check_updated_at CHECK (updated_at >= created_at)
);

-- Tabela CLIENT_INVESTMENT (Investimentos do Cliente)
CREATE TABLE CLIENT_INVESTMENT (
    client_id INTEGER NOT NULL,
    investment_id INTEGER NOT NULL,
    investment_date TIMESTAMP NOT NULL,
    quantity INTEGER NOT NULL CHECK (quantity >= 0),
    invested_amount DECIMAL(25,2) NOT NULL,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (client_id, investment_id),
    FOREIGN KEY (client_id) REFERENCES CLIENT(client_id) ON DELETE CASCADE,
    FOREIGN KEY (investment_id) REFERENCES INVESTMENT(investment_id) ON DELETE RESTRICT,
    CONSTRAINT check_updated_at CHECK (updated_at >= created_at)
);

-- Tabela para rastrear mudanças de status de compliance
CREATE TABLE COMPLIANCE_HISTORY (
    history_id SERIAL PRIMARY KEY,
    client_id INTEGER NOT NULL,
    previous_status BOOLEAN,
    new_status BOOLEAN,
    change_date TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (client_id) REFERENCES CLIENT(client_id) ON DELETE CASCADE
);

-- Tabela ACTIVITY_LOG
CREATE TABLE ACTIVITY_LOG (
    log_id SERIAL PRIMARY KEY,
    table_name VARCHAR(100) NOT NULL,
    row_id INTEGER NOT NULL,
    operation_type VARCHAR(50) NOT NULL,
    performed_by VARCHAR(100),
    old_data JSONB,
    new_data JSONB,
    details TEXT,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Índices para melhorar a performance de consultas frequentes
CREATE INDEX idx_client_advisor ON CLIENT(advisor_id);
CREATE INDEX idx_client_investment_date ON CLIENT_INVESTMENT(investment_date);
CREATE INDEX idx_investment_type ON INVESTMENT(type);
CREATE INDEX idx_client_investment_amount ON CLIENT_INVESTMENT (invested_amount);
CREATE INDEX idx_compliance_history_client ON COMPLIANCE_HISTORY(client_id);
CREATE INDEX idx_activity_log_created_at ON ACTIVITY_LOG(created_at);
CREATE INDEX idx_activity_log_table_name ON ACTIVITY_LOG(table_name);
CREATE INDEX idx_activity_log_row_id ON ACTIVITY_LOG(row_id);

-- Regras de negócio implementadas como constraints e triggers

-- Função para verificar se há não conformidade entre os perfis de risco
CREATE OR REPLACE FUNCTION check_risk_profile_compliance()
RETURNS TRIGGER AS $$
BEGIN
    -- Se os perfis de risco forem diferentes, marcar como não conforme (compliance = true)
    IF NEW.risk_profile_wallet != NEW.risk_profile_form THEN
        NEW.compliance := TRUE;
    ELSE
        NEW.compliance := FALSE;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Aplicar o trigger na tabela CLIENT
CREATE TRIGGER check_client_compliance
BEFORE INSERT OR UPDATE OF risk_profile_wallet, risk_profile_form ON CLIENT
FOR EACH ROW
EXECUTE FUNCTION check_risk_profile_compliance();

-- Função para atualizar o timestamp de updated_at
CREATE OR REPLACE FUNCTION update_timestamp()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Aplicar o trigger de atualização de timestamp em todas as tabelas
CREATE TRIGGER update_advisor_timestamp
BEFORE UPDATE ON ADVISOR
FOR EACH ROW
EXECUTE FUNCTION update_timestamp();

CREATE TRIGGER update_client_timestamp
BEFORE UPDATE ON CLIENT
FOR EACH ROW
EXECUTE FUNCTION update_timestamp();

CREATE TRIGGER update_investment_timestamp
BEFORE UPDATE ON INVESTMENT
FOR EACH ROW
EXECUTE FUNCTION update_timestamp();

CREATE TRIGGER update_client_investment_timestamp
BEFORE UPDATE ON CLIENT_INVESTMENT
FOR EACH ROW
EXECUTE FUNCTION update_timestamp();

-- Função para registrar atividades no log
CREATE OR REPLACE FUNCTION log_activity(
    p_client_id INTEGER,
    p_investment_id INTEGER,
    p_action VARCHAR(50),
    p_details TEXT
) RETURNS VOID AS $$
BEGIN
    INSERT INTO ACTIVITY_LOG (client_id, investment_id, action, details)
    VALUES (p_client_id, p_investment_id, p_action, p_details);
END;
$$ LANGUAGE plpgsql;

-- Trigger para registrar mudanças de compliance
CREATE OR REPLACE FUNCTION log_compliance_changes()
RETURNS TRIGGER AS $$
BEGIN
    IF OLD.compliance IS DISTINCT FROM NEW.compliance THEN
        PERFORM log_activity(
            NEW.client_id,
            NULL,
            CASE WHEN NEW.compliance THEN 'NON_COMPLIANT' ELSE 'COMPLIANT' END,
            'Cliente mudou para ' || CASE WHEN NEW.compliance THEN 'não conforme' ELSE 'conforme' END ||
            '. Perfil formulário: ' || NEW.risk_profile_form || ', Perfil carteira: ' || NEW.risk_profile_wallet
        );
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER log_client_compliance_changes
AFTER UPDATE OF compliance ON CLIENT
FOR EACH ROW
EXECUTE FUNCTION log_compliance_changes();

-- Trigger para registrar mudanças de status de compliance no histórico
CREATE OR REPLACE FUNCTION track_compliance_changes()
RETURNS TRIGGER AS $$
BEGIN
    IF OLD.compliance IS DISTINCT FROM NEW.compliance THEN
        INSERT INTO COMPLIANCE_HISTORY (client_id, previous_status, new_status)
        VALUES (NEW.client_id, OLD.compliance, NEW.compliance);
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER track_client_compliance_changes
AFTER UPDATE OF risk_profile_wallet, risk_profile_form ON CLIENT
FOR EACH ROW
EXECUTE FUNCTION track_compliance_changes();

-- View para clientes não conformes
CREATE OR REPLACE VIEW vw_non_compliant_clients AS
SELECT 
    c.client_id,
    c.name,
    c.email,
    c.risk_profile_form,
    c.risk_profile_wallet,
    a.advisor_id,
    COUNT(ci.investment_id) AS total_investments,
    SUM(ci.invested_amount) AS total_invested,
    -- Quando o cliente entrou em não conformidade
    (SELECT MAX(change_date) 
     FROM COMPLIANCE_HISTORY 
     WHERE client_id = c.client_id AND new_status = TRUE) AS non_compliance_since
FROM 
    CLIENT c
JOIN 
    ADVISOR a ON c.advisor_id = a.advisor_id
LEFT JOIN 
    CLIENT_INVESTMENT ci ON c.client_id = ci.client_id
WHERE 
    c.compliance = TRUE
GROUP BY 
    c.client_id, c.name, c.email, c.risk_profile_form, c.risk_profile_wallet, a.advisor_id;

-- Procedure para dados normalizados de investimento
CREATE OR REPLACE PROCEDURE prepare_normalized_investment_data(OUT normalized_data REFCURSOR)
LANGUAGE plpgsql
AS $$
BEGIN
    -- Abrir o cursor para retornar os dados normalizados
    OPEN normalized_data FOR
    WITH stats AS (
        SELECT 
            MIN(invested_amount) AS min_amount, 
            MAX(invested_amount) AS max_amount 
        FROM CLIENT_INVESTMENT
        WHERE invested_amount > 0
    )
    SELECT 
        ci.client_id,
        ci.investment_id,
        CASE 
            WHEN stats.max_amount = stats.min_amount THEN 0.5 -- Evitar divisão por zero
            ELSE (ci.invested_amount - stats.min_amount) / (stats.max_amount - stats.min_amount)
        END AS normalized_amount
    FROM 
        CLIENT_INVESTMENT ci,
        stats
    WHERE 
        ci.invested_amount > 0;
END;
$$;

-- Função para registrar auditoria de operações DML
CREATE OR REPLACE FUNCTION audit_dml_operations()
RETURNS TRIGGER AS $$
DECLARE
    v_old_data JSONB;
    v_new_data JSONB;
    v_row_id INTEGER;
BEGIN
    -- Captura o ID da linha com base na tabela
    IF TG_TABLE_NAME = 'advisor' THEN
        v_row_id := COALESCE(NEW.advisor_id, OLD.advisor_id);
    ELSIF TG_TABLE_NAME = 'client' THEN
        v_row_id := COALESCE(NEW.client_id, OLD.client_id);
    ELSIF TG_TABLE_NAME = 'investment' THEN
        v_row_id := COALESCE(NEW.investment_id, OLD.investment_id);
    ELSIF TG_TABLE_NAME = 'client_investment' THEN
        v_row_id := COALESCE(NEW.client_id, OLD.client_id);
    ELSE
        v_row_id := NULL;
    END IF;

    IF (TG_OP = 'INSERT') THEN
        v_new_data := to_jsonb(NEW);
        INSERT INTO ACTIVITY_LOG (
            table_name,
            row_id,
            operation_type,
            performed_by,
            new_data,
            details
        ) VALUES (
            TG_TABLE_NAME,
            v_row_id,
            'INSERT',
            CURRENT_USER, -- Ou outro valor se o usuário da aplicação for passado
            v_new_data,
            'Novo registro inserido na tabela ' || TG_TABLE_NAME
        );
        RETURN NEW;
    ELSIF (TG_OP = 'UPDATE') THEN
        v_old_data := to_jsonb(OLD);
        v_new_data := to_jsonb(NEW);
        INSERT INTO ACTIVITY_LOG (
            table_name,
            row_id,
            operation_type,
            performed_by,
            old_data,
            new_data,
            details
        ) VALUES (
            TG_TABLE_NAME,
            v_row_id,
            'UPDATE',
            CURRENT_USER,
            v_old_data,
            v_new_data,
            'Registro atualizado na tabela ' || TG_TABLE_NAME
        );
        RETURN NEW;
    ELSIF (TG_OP = 'DELETE') THEN
        v_old_data := to_jsonb(OLD);
        INSERT INTO ACTIVITY_LOG (
            table_name,
            row_id,
            operation_type,
            performed_by,
            old_data,
            details
        ) VALUES (
            TG_TABLE_NAME,
            v_row_id,
            'DELETE',
            CURRENT_USER,
            v_old_data,
            'Registro excluído da tabela ' || TG_TABLE_NAME
        );
        RETURN OLD;
    END IF;

    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Trigger para a tabela ADVISOR
CREATE TRIGGER trg_audit_advisor
AFTER INSERT OR UPDATE OR DELETE ON ADVISOR
FOR EACH ROW EXECUTE FUNCTION audit_dml_operations();

-- Trigger para a tabela CLIENT
CREATE TRIGGER trg_audit_client
AFTER INSERT OR UPDATE OR DELETE ON CLIENT
FOR EACH ROW EXECUTE FUNCTION audit_dml_operations();

-- Trigger para a tabela INVESTMENT
CREATE TRIGGER trg_audit_investment
AFTER INSERT OR UPDATE OR DELETE ON INVESTMENT
FOR EACH ROW EXECUTE FUNCTION audit_dml_operations();

-- Trigger para a tabela CLIENT_INVESTMENT
CREATE TRIGGER trg_audit_client_investment
AFTER INSERT OR UPDATE OR DELETE ON CLIENT_INVESTMENT
FOR EACH ROW EXECUTE FUNCTION audit_dml_operations();

-- Comentários nas tabelas e colunas para documentação
COMMENT ON TABLE ADVISOR IS 'Assessores de investimento responsáveis pelos clientes';
COMMENT ON COLUMN ADVISOR.advisor_id IS 'Identificador único do assessor';
COMMENT ON COLUMN ADVISOR.uid IS 'Identificador único universal do assessor para integração com sistemas externos';
COMMENT ON COLUMN ADVISOR.created_at IS 'Data e hora de criação do registro';
COMMENT ON COLUMN ADVISOR.updated_at IS 'Data e hora da última atualização do registro';

COMMENT ON TABLE CLIENT IS 'Clientes da plataforma de investimentos';
COMMENT ON COLUMN CLIENT.client_id IS 'Identificador único do cliente';
COMMENT ON COLUMN CLIENT.name IS 'Nome completo do cliente';
COMMENT ON COLUMN CLIENT.phone_number IS 'Número de telefone do cliente';
COMMENT ON COLUMN CLIENT.email IS 'Endereço de e-mail do cliente (único)';
COMMENT ON COLUMN CLIENT.risk_profile_wallet IS 'Perfil de risco baseado na carteira atual';
COMMENT ON COLUMN CLIENT.risk_profile_form IS 'Perfil de risco baseado no formulário de avaliação';
COMMENT ON COLUMN CLIENT.compliance IS 'Indica se há divergência entre os perfis de risco (true=não conforme, false=conforme)';
COMMENT ON COLUMN CLIENT.advisor_id IS 'Referência ao assessor responsável pelo cliente';
COMMENT ON COLUMN CLIENT.created_at IS 'Data e hora de criação do registro';
COMMENT ON COLUMN CLIENT.updated_at IS 'Data e hora da última atualização do registro';

COMMENT ON TABLE INVESTMENT IS 'Investimentos disponíveis na plataforma';
COMMENT ON COLUMN INVESTMENT.investment_id IS 'Identificador único do investimento';
COMMENT ON COLUMN INVESTMENT.name IS 'Nome do investimento';
COMMENT ON COLUMN INVESTMENT.type IS 'Tipo do investimento';
COMMENT ON COLUMN INVESTMENT.created_at IS 'Data e hora de criação do registro';
COMMENT ON COLUMN INVESTMENT.updated_at IS 'Data e hora da última atualização do registro';

COMMENT ON TABLE CLIENT_INVESTMENT IS 'Investimentos que cada cliente possui';
COMMENT ON COLUMN CLIENT_INVESTMENT.client_id IS 'Referência ao cliente';
COMMENT ON COLUMN CLIENT_INVESTMENT.investment_id IS 'Referência ao investimento';
COMMENT ON COLUMN CLIENT_INVESTMENT.investment_date IS 'Data em que o investimento foi adquirido';
COMMENT ON COLUMN CLIENT_INVESTMENT.quantity IS 'Quantidade do investimento adquirido';
COMMENT ON COLUMN CLIENT_INVESTMENT.invested_amount IS 'Montante financeiro aplicado no investimento';
COMMENT ON COLUMN CLIENT_INVESTMENT.created_at IS 'Data e hora de criação do registro';
COMMENT ON COLUMN CLIENT_INVESTMENT.updated_at IS 'Data e hora da última atualização do registro';

COMMENT ON TABLE ACTIVITY_LOG IS 'Log detalhado de todas as operações de DML no sistema para fins de auditoria e rastreabilidade.';
COMMENT ON COLUMN ACTIVITY_LOG.table_name IS 'Nome da tabela em que a operação ocorreu.';
COMMENT ON COLUMN ACTIVITY_LOG.row_id IS 'ID da linha afetada pela operação (PK da tabela afetada).';
COMMENT ON COLUMN ACTIVITY_LOG.operation_type IS 'Tipo de operação SQL (INSERT, UPDATE, DELETE).';
COMMENT ON COLUMN ACTIVITY_LOG.performed_by IS 'Usuário do banco de dados que executou a operação.';
COMMENT ON COLUMN ACTIVITY_LOG.old_data IS 'Estado da linha antes da operação (JSONB). Preenchido para UPDATE e DELETE.';
COMMENT ON COLUMN ACTIVITY_LOG.new_data IS 'Estado da linha após a operação (JSONB). Preenchido para INSERT e UPDATE.';
COMMENT ON COLUMN ACTIVITY_LOG.details IS 'Detalhes adicionais ou comentários sobre a operação.';
COMMENT ON COLUMN ACTIVITY_LOG.created_at IS 'Timestamp da ocorrência da operação.';

COMMENT ON VIEW vw_non_compliant_clients IS 'View que lista todos os clientes em situação de não conformidade';

COMMENT ON FUNCTION log_activity(INTEGER, INTEGER, VARCHAR, TEXT) IS 'Registra uma atividade no log do sistema';
