using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Pati.ClassificationService.Application.Services;

namespace Pati.ClassificationService.Infrastructure.Jobs
{
    /// <summary>
    /// Job simples usando Timer para classificação automática
    /// Alternativa mais simples ao Quartz.NET
    /// </summary>
    public class SimpleClassificationJob : BackgroundService
    {
        private readonly ILogger<SimpleClassificationJob> _logger;
        private readonly IServiceProvider _serviceProvider;
        private readonly ClassificationJobOptions _options;
        private Timer? _timer;

        public SimpleClassificationJob(
            ILogger<SimpleClassificationJob> logger,
            IServiceProvider serviceProvider,
            IOptions<ClassificationJobOptions> options)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _serviceProvider = serviceProvider ?? throw new ArgumentNullException(nameof(serviceProvider));
            _options = options?.Value ?? throw new ArgumentNullException(nameof(options));
        }

        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            Console.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] 🔧 CONFIGURAÇÃO: Job habilitado = {_options.Enabled}");
            Console.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] 🔧 CONFIGURAÇÃO: Intervalo = {_options.IntervalMinutes} minutos");
            Console.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] 🔧 CONFIGURAÇÃO: Executar na inicialização = {_options.RunOnStartup}");
            
            _logger.LogInformation("SimpleClassificationJob ExecuteAsync called - Enabled: {Enabled}, Interval: {Minutes}min, RunOnStartup: {RunOnStartup}",
                _options.Enabled, _options.IntervalMinutes, _options.RunOnStartup);

            if (!_options.Enabled)
            {
                Console.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] ❌ DESABILITADO: Job de classificação está desabilitado na configuração");
                _logger.LogInformation("Classification job is disabled in configuration");
                return;
            }

            Console.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] 🕒 INICIANDO: Simple Classification Job Service");
            _logger.LogInformation("🕒 Starting Simple Classification Job Service");

            if (_options.IntervalMinutes >= 1440)
            {
                var hours = _options.IntervalMinutes / 60;
                Console.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] 🔄 CONFIGURAÇÃO: Job executará a cada {hours} horas (Diariamente às {_options.DailyExecutionTime ?? "não especificado"}, Executar na inicialização: {_options.RunOnStartup})");
                _logger.LogInformation("🔄 Classification job will run every {Hours} hours (Daily at {DailyTime}, RunOnStartup: {RunOnStartup})",
                    hours, _options.DailyExecutionTime ?? "not specified", _options.RunOnStartup);
            }
            else
            {
                Console.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] 🔄 CONFIGURAÇÃO: Job executará a cada {_options.IntervalMinutes} minutos (Executar na inicialização: {_options.RunOnStartup})");
                _logger.LogInformation("🔄 Classification job will run every {Minutes} minutes (RunOnStartup: {RunOnStartup})",
                    _options.IntervalMinutes, _options.RunOnStartup);
            }

            // Calcular intervalo
            var interval = TimeSpan.FromMinutes(_options.IntervalMinutes);
            var initialDelay = _options.RunOnStartup ? TimeSpan.Zero : interval;

            Console.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] ⏰ TIMER: Intervalo = {interval.TotalMinutes} minutos, Delay inicial = {initialDelay.TotalSeconds} segundos");

            // Configurar timer
            Console.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] ⏰ TIMER: Configurando timer...");
            _timer = new Timer(
                callback: async _ => await ExecuteJobAsync(),
                state: null,
                dueTime: initialDelay,
                period: interval);

            Console.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] ⏰ TIMER: Timer configurado com sucesso");
            Console.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] 🔄 AGUARDANDO: Job aguardando execução...");

            // Manter o serviço rodando
            await Task.Delay(Timeout.Infinite, stoppingToken);
        }

        private async Task ExecuteJobAsync()
        {
            Console.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] 🔔 TIMER: Callback do timer executado!");
            var startTime = DateTime.UtcNow;

            try
            {
                Console.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] 🚀 INÍCIO: Job de classificação iniciado");
                _logger.LogInformation("🚀 Starting scheduled classification job at {StartTime}", startTime);

                // Criar scope para resolver serviços
                using var scope = _serviceProvider.CreateScope();
                var classificationService = scope.ServiceProvider.GetRequiredService<IClassificationService>();

                Console.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] 📞 CHAMADA: Chamando ClassificationService.ClassifyAllPortfoliosAsync()");
                
                // Executar classificação de todas as carteiras
                await classificationService.ClassifyAllPortfoliosAsync();

                var duration = DateTime.UtcNow - startTime;
                Console.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] ✅ SUCESSO: Job de classificação concluído em {duration.TotalMilliseconds:F0}ms");
                _logger.LogInformation("✅ Scheduled classification job completed successfully in {Duration}ms",
                    duration.TotalMilliseconds);
            }
            catch (Exception ex)
            {
                var duration = DateTime.UtcNow - startTime;
                Console.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] ❌ ERRO: Job de classificação falhou após {duration.TotalMilliseconds:F0}ms - {ex.Message}");
                _logger.LogError(ex, "Scheduled classification job failed after {Duration}ms", 
                    duration.TotalMilliseconds);
            }
        }

        public override async Task StopAsync(CancellationToken cancellationToken)
        {
            _logger.LogInformation("Stopping Simple Classification Job Service");

            _timer?.Change(Timeout.Infinite, 0);
            _timer?.Dispose();

            await base.StopAsync(cancellationToken);
            
            _logger.LogInformation("Simple Classification Job Service stopped");
        }

        public override void Dispose()
        {
            _timer?.Dispose();
            base.Dispose();
        }
    }
}
