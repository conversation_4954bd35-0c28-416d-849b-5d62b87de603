#!/usr/bin/env python3
"""
Script de configuração e execução da população do banco de dados
com dados do CSV e API randomuser.me
"""

import os
import sys
import subprocess
import json
from pathlib import Path

def install_dependencies():
    """Instala as dependências necessárias"""
    print("📦 Instalando dependências...")
    
    dependencies = [
        'pandas',
        'requests', 
        'psycopg2-binary'
    ]
    
    for dep in dependencies:
        try:
            subprocess.check_call([sys.executable, '-m', 'pip', 'install', dep])
            print(f"✅ {dep} instalado com sucesso")
        except subprocess.CalledProcessError as e:
            print(f"❌ Erro ao instalar {dep}: {e}")
            return False
    
    return True

def create_config_file():
    """Cria arquivo de configuração do banco de dados"""
    script_dir = Path(__file__).parent  # Pasta onde está o script
    config_path = script_dir / 'db_config.json'
    
    if config_path.exists():
        print("📋 Arquivo de configuração já existe")
        return True
    
    print("📋 Criando arquivo de configuração do banco...")
    
    config = {
        "host": "localhost",
        "database": "mean_girls",
        "user": "inteli", 
        "password": "inteli@123",
        "port": "5432"
}
    
    with open(config_path, 'w') as f:
        json.dump(config, f, indent=2)
    
    print("✅ Arquivo db_config.json criado")
    print("⚠️  IMPORTANTE: Edite db_config.json com suas credenciais do banco")
    
    return True

def check_csv_file():
    """Verifica se o arquivo CSV existe"""
    csv_path = Path('/Users/<USER>/Documents/GitHub/2025-1B-T13-ES06-G02/src/dataset/PosicaoIntelli.csv')
    
    if not csv_path.exists():
        print(f"❌ Arquivo CSV não encontrado: {csv_path}")
        print("📁 Certifique-se de que o arquivo está em src/dataset/PosicaoIntelli.csv")
        return False
    
    print(f"✅ Arquivo CSV encontrado: {csv_path}")
    return True

def run_sql_setup(db_config):
    """Executa o script SQL de configuração da distribuição automática"""
    print("🗄️  Executando configuração SQL...")
    
    # Corrige o caminho do script SQL baseado na localização atual
    current_dir = Path(__file__).parent
    sql_file = current_dir.parent.parent.parent / 'scripts' / '03_distribuicao_automatica_clientes.sql'
    
    if not sql_file.exists():
        print(f"❌ Script SQL não encontrado: {sql_file}")
        print("📁 Tentando caminhos alternativos...")
        
        # Tenta caminhos alternativos
        alternative_paths = [
            Path('../scripts/03_distribuicao_automatica_clientes.sql'),
            Path('../../scripts/03_distribuicao_automatica_clientes.sql'),
            Path('../../../scripts/03_distribuicao_automatica_clientes.sql'),
            current_dir / 'scripts' / '03_distribuicao_automatica_clientes.sql'
        ]
        
        for alt_path in alternative_paths:
            if alt_path.exists():
                sql_file = alt_path
                print(f"✅ Script encontrado em: {sql_file}")
                break
        else:
            print("❌ Script SQL não encontrado em nenhum local")
            return False
    
    try:
        # Comando psql corrigido - formato correto dos parâmetros
        cmd = [
            'psql',
            f"--host={db_config['host']}",
            f"--port={db_config['port']}", 
            f"--dbname={db_config['database']}",
            f"--username={db_config['user']}",
            '--file', str(sql_file)
        ]
        
        print(f"🔧 Executando comando: {' '.join(cmd)}")
        
        env = os.environ.copy()
        env['PGPASSWORD'] = db_config['password']
        
        result = subprocess.run(cmd, env=env, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ Configuração SQL executada com sucesso")
            if result.stdout:
                print(f"📝 Saída: {result.stdout}")
            return True
        else:
            print(f"❌ Erro na execução SQL: {result.stderr}")
            if result.stdout:
                print(f"📝 Saída: {result.stdout}")
            return False
            
    except FileNotFoundError:
        print("❌ psql não encontrado. Instale o PostgreSQL client")
        print("💡 No macOS: brew install postgresql")
        return False
    except Exception as e:
        print(f"❌ Erro ao executar SQL: {e}")
        return False

def run_population_script():
    """Executa o script de população do banco"""
    print("🚀 Executando população do banco de dados...")
    
    # NOME CORRETO DO ARQUIVO
    script_path = Path('database_populator.py')
    
    if not script_path.exists():
        print(f"❌ Script de população não encontrado: {script_path}")
        return False
    
    try:
        result = subprocess.run([sys.executable, str(script_path)], 
                              capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ População do banco concluída com sucesso!")
            print("\n📊 Saída do script:")
            print(result.stdout)
            return True
        else:
            print(f"❌ Erro na população: {result.stderr}")
            print(f"📝 Saída: {result.stdout}")
            return False
            
    except Exception as e:
        print(f"❌ Erro ao executar script: {e}")
        return False

def main():
    """Função principal"""
    print("🎯 CONFIGURAÇÃO E POPULAÇÃO DO BANCO DE DADOS")
    print("=" * 50)
    
    # 1. Instalar dependências
    if not install_dependencies():
        print("❌ Falha na instalação de dependências")
        return 1
    
    # 2. Criar arquivo de configuração
    if not create_config_file():
        print("❌ Falha na criação do arquivo de configuração")
        return 1
    
    # 3. Verificar arquivo CSV
    if not check_csv_file():
        print("❌ Arquivo CSV não encontrado")
        return 1
    
    # 4. Carregar configuração do banco
    try:
        with open('db_config.json', 'r') as f:
            db_config = json.load(f)
    except Exception as e:
        print(f"❌ Erro ao carregar configuração: {e}")
        return 1
    
    # 5. Executar configuração SQL
    if not run_sql_setup(db_config):
        print("❌ Falha na configuração SQL")
        return 1
    
    # 6. Executar população
    if not run_population_script():
        print("❌ Falha na população do banco")
        return 1
    
    print("\n🎉 PROCESSO CONCLUÍDO COM SUCESSO!")
    print("\n📋 Próximos passos:")
    print("1. Verifique os dados no banco de dados")
    print("2. Execute consultas de validação")
    print("3. Teste a distribuição automática adicionando novos assessores")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())

