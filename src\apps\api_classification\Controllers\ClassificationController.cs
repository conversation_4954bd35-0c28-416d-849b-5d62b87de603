using Microsoft.AspNetCore.Mvc;
using Pati.ClassificationService.Application.Services;
using Pati.ClassificationService.DTOs;
using System.ComponentModel.DataAnnotations;

namespace Pati.ClassificationService.Controllers
{
    /// <summary>
    /// Controller para classificação de carteiras de investimento baseado nas regras CVM 175
    /// </summary>
    [ApiController]
    [Route("internal/v1/portfolios")]
    [Produces("application/json")]
    public class ClassificationController : ControllerBase
    {
        private readonly IClassificationService _classificationService;
        private readonly ILogger<ClassificationController> _logger;

        public ClassificationController(
            IClassificationService classificationService,
            ILogger<ClassificationController> logger)
        {
            _classificationService = classificationService ?? throw new ArgumentNullException(nameof(classificationService));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        /// <summary>
        /// Classifica uma carteira identificando inconformidades baseado nas regras CVM 175 e modelo SVD
        /// </summary>
        /// <param name="portfolioInput">Dados da carteira a ser classificada conforme CSV inconformidades.csv</param>
        /// <returns>Resultado da classificação com inconformidades identificadas</returns>
        /// <response code="200">Classificação realizada com sucesso</response>
        /// <response code="400">Dados de entrada inválidos</response>
        /// <response code="500">Erro interno do servidor</response>
        [HttpPost("classify")]
        [ProducesResponseType(typeof(PortfolioOutputDto), 200)]
        [ProducesResponseType(typeof(ValidationProblemDetails), 400)]
        [ProducesResponseType(500)]
        public async Task<ActionResult<PortfolioOutputDto>> ClassifyPortfolio(
            [FromBody] PortfolioInputDto portfolioInput)
        {
            Console.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] 🌐 HTTP: Requisição POST /internal/v1/portfolios/classify recebida");
            
            try
            {
                // Validar se o DTO foi recebido
                if (portfolioInput == null)
                {
                    Console.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] ❌ HTTP: Portfolio input é null");
                    _logger.LogWarning("Portfolio input is null");
                    return BadRequest(new { error = "Portfolio input is required" });
                }

                Console.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] 🌐 HTTP: Classificando carteira - Cliente {portfolioInput.AccountId}, Carteira {portfolioInput.PortfolioId}");
                _logger.LogInformation("Received portfolio classification request for account {AccountId}, portfolio {PortfolioId}",
                    portfolioInput.AccountId, portfolioInput.PortfolioId);

                // Validar modelo usando IValidatableObject
                var validationResults = portfolioInput.Validate(new System.ComponentModel.DataAnnotations.ValidationContext(portfolioInput));
                if (validationResults.Any())
                {
                    var errors = validationResults.Select(vr => vr.ErrorMessage).ToList();
                    Console.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] ❌ HTTP: Erros de validação: {string.Join(", ", errors)}");
                    _logger.LogWarning("Validation errors: {Errors}", string.Join(", ", errors));
                    return BadRequest(new { errors = errors });
                }

                Console.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] 🌐 HTTP: Chamando ClassificationService.ClassifyPortfolioAsync()");
                var result = await _classificationService.ClassifyPortfolioAsync(portfolioInput);

                Console.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] ✅ HTTP: Classificação concluída - Cliente {portfolioInput.AccountId}, {result.Inconsistencies.Count} inconsistências");
                _logger.LogInformation("Portfolio classification completed successfully for account {AccountId}. Found {InconsistencyCount} inconsistencies",
                    portfolioInput.AccountId, result.Inconsistencies.Count);

                return Ok(result);
            }
            catch (ArgumentException ex)
            {
                Console.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] ❌ HTTP: Argumento inválido - {ex.Message}");
                _logger.LogWarning(ex, "Invalid argument in portfolio classification request");
                return BadRequest(new { error = ex.Message });
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] 💥 HTTP: Erro interno - {ex.Message}");
                _logger.LogError(ex, "Error processing portfolio classification request for account {AccountId}", portfolioInput?.AccountId);
                return StatusCode(500, new { error = "Internal server error occurred while processing the request." });
            }
        }

        /// <summary>
        /// Classifica todas as carteiras em lote
        /// </summary>
        /// <returns>Resultado do processamento em lote</returns>
        /// <response code="200">Processamento em lote realizado com sucesso</response>
        /// <response code="500">Erro interno do servidor</response>
        [HttpPost("classify-all")]
        [ProducesResponseType(typeof(object), 200)]
        [ProducesResponseType(500)]
        public async Task<ActionResult<object>> ClassifyAllPortfolios()
        {
            Console.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] 🌐 HTTP: Requisição POST /internal/v1/portfolios/classify-all recebida");
            
            try
            {
                Console.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] 🌐 HTTP: Iniciando classificação em lote via HTTP");
                _logger.LogInformation("Starting batch portfolio classification");

                Console.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] 🌐 HTTP: Chamando ClassificationService.ClassifyAllPortfoliosAsync()");
                await _classificationService.ClassifyAllPortfoliosAsync();

                var result = new
                {
                    Message = "Batch classification completed successfully",
                    Timestamp = DateTime.UtcNow
                };

                Console.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] ✅ HTTP: Classificação em lote concluída com sucesso");
                _logger.LogInformation("Batch portfolio classification completed successfully");

                return Ok(result);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] 💥 HTTP: Erro na classificação em lote - {ex.Message}");
                _logger.LogError(ex, "Error processing batch portfolio classification");
                return StatusCode(500, new { error = "Internal server error occurred while processing batch classification." });
            }
        }

        /// <summary>
        /// Endpoint manual para testar a classificação (para desenvolvimento/debug)
        /// </summary>
        /// <returns>Resultado do processamento manual</returns>
        /// <response code="200">Processamento manual realizado com sucesso</response>
        /// <response code="500">Erro interno do servidor</response>
        [HttpPost("test-manual")]
        [ProducesResponseType(typeof(object), 200)]
        [ProducesResponseType(500)]
        public async Task<ActionResult<object>> TestManualClassification()
        {
            Console.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] 🌐 HTTP: Requisição POST /internal/v1/portfolios/test-manual recebida (TESTE MANUAL)");
            
            try
            {
                Console.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] 🌐 HTTP: Iniciando classificação manual via HTTP");
                _logger.LogInformation("Starting manual classification test");

                Console.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] 🌐 HTTP: Chamando ClassificationService.ClassifyAllPortfoliosAsync()");
                var result = await _classificationService.ClassifyAllPortfoliosAsync();

                Console.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] ✅ HTTP: Classificação manual concluída com sucesso");
                _logger.LogInformation("Manual classification test completed successfully");

                return Ok(result);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] 💥 HTTP: Erro na classificação manual - {ex.Message}");
                _logger.LogError(ex, "Error processing manual classification test");
                return StatusCode(500, new { error = "Internal server error occurred while processing manual classification test." });
            }
        }

        /// <summary>
        /// Verifica o status de saúde do serviço de classificação
        /// </summary>
        /// <returns>Status de saúde do serviço</returns>
        /// <response code="200">Serviço funcionando corretamente</response>
        [HttpGet("health")]
        [ProducesResponseType(200)]
        public ActionResult<object> GetHealth()
        {
            return Ok(new
            {
                status = "healthy",
                timestamp = DateTime.UtcNow,
                service = "Pati.ClassificationService",
                version = "1.0.0",
                description = "Serviço focado em classificação de carteiras usando modelo SVD",
                endpoints = new[]
                {
                    "POST /internal/v1/portfolios/classify - Classifica carteira individual",
                    "POST /internal/v1/portfolios/classify-all - Classifica todas as carteiras em lote",
                    "POST /internal/v1/portfolios/test-manual - Teste manual da classificação",
                    "GET /internal/v1/portfolios/health - Health check do serviço"
                },
                svdModel = new
                {
                    loaded = true,
                    description = "Modelo SVD para classificação de carteiras baseado no notebook SVD_CollaborativeFiltering_CVM175.ipynb"
                }
            });
        }
    }
}
