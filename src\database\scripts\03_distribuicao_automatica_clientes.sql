-- SCRIPT DE DISTRIBUIÇÃO AUTOMÁTICA DE CLIENTES
-- Este script implementa a lógica para distribuir automaticamente
-- clientes entre assessores quando novos assessores são cadastrados


-- 1. FUNÇÃO PARA REDISTRIBUIR CLIENTES ALEATORIAMENTE

CREATE OR REPLACE FUNCTION redistribute_clients_among_advisors()
RETURNS VOID AS $$
DECLARE
    total_clients INTEGER;
    total_advisors INTEGER;
    clients_per_advisor INTEGER;
    remaining_clients INTEGER;
    advisor_record RECORD;
    client_record RECORD;
    current_advisor_id INTEGER;
    advisor_client_count INTEGER;
    advisor_index INTEGER := 0;
BEGIN
    -- Contar total de clientes e assessores
    SELECT COUNT(*) INTO total_clients FROM CLIENT;
    SELECT COUNT(*) INTO total_advisors FROM ADVISOR;
    
    -- Se não há assessores ou clientes, não fazer nada
    IF total_advisors = 0 OR total_clients = 0 THEN
        RAISE NOTICE 'Não há assessores ou clientes para redistribuir';
        RETURN;
    END IF;
    
    -- Calcular distribuição
    clients_per_advisor := total_clients / total_advisors;
    remaining_clients := total_clients % total_advisors;
    
    RAISE NOTICE 'Redistribuindo % clientes entre % assessores', total_clients, total_advisors;
    RAISE NOTICE 'Base: % clientes por assessor, % clientes extras', clients_per_advisor, remaining_clients;
    
    -- Criar array temporário com todos os client_ids embaralhados
    CREATE TEMP TABLE temp_shuffled_clients AS
    SELECT client_id, ROW_NUMBER() OVER (ORDER BY RANDOM()) as rn
    FROM CLIENT;
    
    -- Redistribuir clientes
    advisor_index := 0;
    
    FOR advisor_record IN 
        SELECT advisor_id FROM ADVISOR ORDER BY advisor_id
    LOOP
        advisor_index := advisor_index + 1;
        
        -- Calcular quantos clientes este assessor deve receber
        advisor_client_count := clients_per_advisor;
        IF advisor_index <= remaining_clients THEN
            advisor_client_count := advisor_client_count + 1;
        END IF;
        
        -- Atualizar clientes para este assessor
        UPDATE CLIENT 
        SET advisor_id = advisor_record.advisor_id,
            updated_at = CURRENT_TIMESTAMP
        WHERE client_id IN (
            SELECT client_id 
            FROM temp_shuffled_clients 
            WHERE rn BETWEEN 
                ((advisor_index - 1) * clients_per_advisor + LEAST(advisor_index - 1, remaining_clients) + 1)
                AND 
                (advisor_index * clients_per_advisor + LEAST(advisor_index, remaining_clients))
        );
        
        RAISE NOTICE 'Assessor % recebeu % clientes', advisor_record.advisor_id, advisor_client_count;
    END LOOP;
    
    -- Limpar tabela temporária
    DROP TABLE temp_shuffled_clients;
    
    -- Log da redistribuição
    INSERT INTO ACTIVITY_LOG (client_id, action, details)
    SELECT NULL, 'REDISTRIBUTION', 
           'Redistribuição automática: ' || total_clients || ' clientes entre ' || total_advisors || ' assessores';
    
    RAISE NOTICE 'Redistribuição concluída com sucesso';
END;
$$ LANGUAGE plpgsql;


-- 2. FUNÇÃO PARA DISTRIBUIR NOVOS CLIENTES


CREATE OR REPLACE FUNCTION assign_client_to_advisor()
RETURNS TRIGGER AS $$
DECLARE
    advisor_with_least_clients INTEGER;
    min_client_count INTEGER;
BEGIN
    -- Se o advisor_id já foi definido, não fazer nada
    IF NEW.advisor_id IS NOT NULL THEN
        RETURN NEW;
    END IF;
    
    -- Encontrar o assessor com menos clientes
    SELECT a.advisor_id, COUNT(c.client_id) as client_count
    INTO advisor_with_least_clients, min_client_count
    FROM ADVISOR a
    LEFT JOIN CLIENT c ON a.advisor_id = c.advisor_id
    GROUP BY a.advisor_id
    ORDER BY COUNT(c.client_id), RANDOM()  -- Random para desempate
    LIMIT 1;
    
    -- Se não há assessores, gerar erro
    IF advisor_with_least_clients IS NULL THEN
        RAISE EXCEPTION 'Nenhum assessor disponível para atribuir ao cliente';
    END IF;
    
    -- Atribuir o assessor ao cliente
    NEW.advisor_id := advisor_with_least_clients;
    
    -- Log da atribuição
    INSERT INTO ACTIVITY_LOG (client_id, action, details)
    VALUES (NEW.client_id, 'AUTO_ASSIGNMENT', 
            'Cliente automaticamente atribuído ao assessor ' || advisor_with_least_clients);
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;


-- 3. TRIGGER PARA REDISTRIBUIÇÃO AUTOMÁTICA


CREATE OR REPLACE FUNCTION trigger_redistribution_on_new_advisor()
RETURNS TRIGGER AS $$
BEGIN
    -- Temporariamente permitir NULL no client_id para logs de sistema
    ALTER TABLE ACTIVITY_LOG ALTER COLUMN client_id DROP NOT NULL;
    
    -- Executar redistribuição após inserção de novo assessor
    PERFORM redistribute_clients_among_advisors();
    
    -- Voltar a exigir client_id para logs normais
    ALTER TABLE ACTIVITY_LOG ALTER COLUMN client_id SET NOT NULL;
    
    RAISE NOTICE 'Novo assessor cadastrado (ID: %), redistribuição automática executada', NEW.advisor_id;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;


-- 4. TRIGGER PARA ATRIBUIÇÃO AUTOMÁTICA DE NOVOS CLIENTES


-- Criar trigger para atribuição automática de novos clientes
DROP TRIGGER IF EXISTS auto_assign_advisor_to_client ON CLIENT;
CREATE TRIGGER auto_assign_advisor_to_client
    BEFORE INSERT ON CLIENT
    FOR EACH ROW
    EXECUTE FUNCTION assign_client_to_advisor();


-- 5. FUNÇÃO PARA BALANCEAMENTO MANUAL


CREATE OR REPLACE FUNCTION balance_advisor_workload()
RETURNS TABLE(
    advisor_id INTEGER,
    client_count BIGINT,
    status TEXT
) AS $$
DECLARE
    avg_clients_per_advisor NUMERIC;
    advisor_record RECORD;
    excess_clients INTEGER;
    needed_clients INTEGER;
BEGIN
    -- Calcular média de clientes por assessor
    SELECT AVG(client_count) INTO avg_clients_per_advisor
    FROM (
        SELECT COUNT(c.client_id) as client_count
        FROM ADVISOR a
        LEFT JOIN CLIENT c ON a.advisor_id = c.advisor_id
        GROUP BY a.advisor_id
    ) sub;
    
    RAISE NOTICE 'Média de clientes por assessor: %', avg_clients_per_advisor;
    
    -- Retornar estatísticas de cada assessor
    RETURN QUERY
    SELECT 
        a.advisor_id,
        COUNT(c.client_id) as client_count,
        CASE 
            WHEN COUNT(c.client_id) > avg_clients_per_advisor * 1.2 THEN 'SOBREGREGADO'
            WHEN COUNT(c.client_id) < avg_clients_per_advisor * 0.8 THEN 'SUBCARREGADO'
            ELSE 'BALANCEADO'
        END as status
    FROM ADVISOR a
    LEFT JOIN CLIENT c ON a.advisor_id = c.advisor_id
    GROUP BY a.advisor_id
    ORDER BY COUNT(c.client_id) DESC;
END;
$$ LANGUAGE plpgsql;


-- 6. VIEW PARA MONITORAMENTO DE DISTRIBUIÇÃO


CREATE OR REPLACE VIEW vw_advisor_distribution AS
SELECT 
    a.advisor_id,
    a.uid,
    COUNT(c.client_id) as total_clients,
    COUNT(CASE WHEN c.compliance = TRUE THEN 1 END) as non_compliant_clients,
    COALESCE(SUM(ci.invested_amount), 0) as total_aum,
    COUNT(DISTINCT ci.investment_id) as unique_investments,
    ROUND(
        (COUNT(c.client_id) * 100.0 / (SELECT COUNT(*) FROM CLIENT)), 2
    ) as percentage_of_total_clients
FROM ADVISOR a
LEFT JOIN CLIENT c ON a.advisor_id = c.advisor_id
LEFT JOIN CLIENT_INVESTMENT ci ON c.client_id = ci.client_id
GROUP BY a.advisor_id, a.uid
ORDER BY total_clients DESC;


-- 7. FUNÇÃO PARA RELATÓRIO DE DISTRIBUIÇÃO


CREATE OR REPLACE FUNCTION generate_distribution_report()
RETURNS TABLE(
    metric TEXT,
    value TEXT
) AS $$
DECLARE
    total_advisors INTEGER;
    total_clients INTEGER;
    avg_clients NUMERIC;
    min_clients INTEGER;
    max_clients INTEGER;
    std_dev NUMERIC;
BEGIN
    -- Estatísticas básicas
    SELECT COUNT(*) INTO total_advisors FROM ADVISOR;
    SELECT COUNT(*) INTO total_clients FROM CLIENT;
    
    SELECT 
        AVG(client_count)::NUMERIC(10,2),
        MIN(client_count),
        MAX(client_count),
        STDDEV(client_count)::NUMERIC(10,2)
    INTO avg_clients, min_clients, max_clients, std_dev
    FROM (
        SELECT COUNT(c.client_id) as client_count
        FROM ADVISOR a
        LEFT JOIN CLIENT c ON a.advisor_id = c.advisor_id
        GROUP BY a.advisor_id
    ) sub;
    
    -- Retornar métricas
    RETURN QUERY VALUES
        ('Total de Assessores', total_advisors::TEXT),
        ('Total de Clientes', total_clients::TEXT),
        ('Média de Clientes por Assessor', avg_clients::TEXT),
        ('Mínimo de Clientes', min_clients::TEXT),
        ('Máximo de Clientes', max_clients::TEXT),
        ('Desvio Padrão', COALESCE(std_dev::TEXT, '0')),
        ('Distribuição Balanceada', 
         CASE WHEN std_dev <= avg_clients * 0.1 THEN 'SIM' ELSE 'NÃO' END);
END;
$$ LANGUAGE plpgsql;


-- 8. PROCEDURE PARA MIGRAÇÃO DE CLIENTES


CREATE OR REPLACE FUNCTION migrate_clients_between_advisors(
    from_advisor_id INTEGER,
    to_advisor_id INTEGER,
    client_count INTEGER DEFAULT NULL
)
RETURNS INTEGER AS $$
DECLARE
    migrated_count INTEGER := 0;
    client_record RECORD;
BEGIN
    -- Validar se os assessores existem
    IF NOT EXISTS (SELECT 1 FROM ADVISOR WHERE advisor_id = from_advisor_id) THEN
        RAISE EXCEPTION 'Assessor origem (%) não encontrado', from_advisor_id;
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM ADVISOR WHERE advisor_id = to_advisor_id) THEN
        RAISE EXCEPTION 'Assessor destino (%) não encontrado', to_advisor_id;
    END IF;
    
    -- Se client_count não especificado, migrar todos
    IF client_count IS NULL THEN
        SELECT COUNT(*) INTO client_count 
        FROM CLIENT 
        WHERE advisor_id = from_advisor_id;
    END IF;
    
    -- Migrar clientes
    UPDATE CLIENT 
    SET advisor_id = to_advisor_id,
        updated_at = CURRENT_TIMESTAMP
    WHERE client_id IN (
        SELECT client_id 
        FROM CLIENT 
        WHERE advisor_id = from_advisor_id 
        ORDER BY RANDOM() 
        LIMIT client_count
    );
    
    GET DIAGNOSTICS migrated_count = ROW_COUNT;
    
    -- Log da migração
    INSERT INTO ACTIVITY_LOG (client_id, action, details)
    VALUES (NULL, 'MIGRATION', 
            FORMAT('Migrados %s clientes do assessor %s para %s', 
                   migrated_count, from_advisor_id, to_advisor_id));
    
    RAISE NOTICE 'Migrados % clientes do assessor % para %', 
                 migrated_count, from_advisor_id, to_advisor_id;
    
    RETURN migrated_count;
END;
$$ LANGUAGE plpgsql;


-- 9. COMENTÁRIOS E DOCUMENTAÇÃO


COMMENT ON FUNCTION redistribute_clients_among_advisors() IS 
'Redistribui todos os clientes aleatoriamente entre todos os assessores de forma equilibrada';

COMMENT ON FUNCTION assign_client_to_advisor() IS 
'Atribui automaticamente um assessor ao cliente baseado na carga de trabalho atual';

COMMENT ON FUNCTION balance_advisor_workload() IS 
'Analisa o balanceamento da carga de trabalho entre assessores';

COMMENT ON VIEW vw_advisor_distribution IS 
'View para monitoramento da distribuição de clientes, AUM e compliance por assessor';

COMMENT ON FUNCTION generate_distribution_report() IS 
'Gera relatório estatístico da distribuição de clientes entre assessores';

COMMENT ON FUNCTION migrate_clients_between_advisors(INTEGER, INTEGER, INTEGER) IS 
'Migra um número específico de clientes entre dois assessores';


-- 10. MENSAGEM DE CONFIRMAÇÃO


\echo 'Script de distribuição automática de clientes instalado com sucesso!'
\echo 'Funcionalidades disponíveis:'
\echo '- Redistribuição automática quando novo assessor é cadastrado'
\echo '- Atribuição automática de novos clientes ao assessor com menor carga'
\echo '- Funções para balanceamento manual e migração de clientes'
\echo '- Views e relatórios para monitoramento da distribuição'

