import pandas as pd
import requests
import psycopg2
import json
import random
import time
from datetime import datetime, timedelta
from typing import Dict, List, Tuple
import logging

# Configuração de logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class DatabasePopulator:
    def __init__(self, db_config: Dict[str, str]):
        """
        Inicializa o populador do banco de dados
        
        Args:
            db_config: Dicionário com configurações do banco (host, database, user, password, port)
        """
        self.db_config = db_config
        self.connection = None
        self.cursor = None
        
    def connect_database(self):
        """Conecta ao banco de dados PostgreSQL"""
        try:
            self.connection = psycopg2.connect(**self.db_config)
            self.cursor = self.connection.cursor()
            logger.info("Conexão com banco de dados estabelecida")
        except Exception as e:
            logger.error(f"Erro ao conectar com banco: {e}")
            raise
    
    def disconnect_database(self):
        """Desconecta do banco de dados"""
        if self.cursor:
            self.cursor.close()
        if self.connection:
            self.connection.close()
        logger.info("Conexão com banco de dados encerrada")
    
    def load_csv_data(self, csv_path: str) -> pd.DataFrame:
        """
        Carrega e processa os dados do CSV
        
        Args:
            csv_path: Caminho para o arquivo CSV
            
        Returns:
            DataFrame com os dados processados
        """
        logger.info(f"Carregando dados do CSV: {csv_path}")
        
        # Definir nomes das colunas baseado na análise
        column_names = ['client_id', 'invested_amount', 'quantity', 'investment_name', 'investment_type', 'risk_profile']
        
        # Carregar CSV
        df = pd.read_csv(csv_path, names=column_names, encoding='utf-8-sig')
        
        # Limpar dados problemáticos
        df = df[df['risk_profile'].isin(['Conservador', 'Moderado', 'Sofisticado'])]
        
        logger.info(f"Dados carregados: {len(df)} registros, {df['client_id'].nunique()} clientes únicos")
        
        return df
    
    def get_unique_clients_with_profile(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Extrai clientes únicos do CSV com seus perfis de risco
        
        Args:
            df: DataFrame com dados do CSV
            
        Returns:
            DataFrame com clientes únicos, perfis de risco e estatísticas de investimento
        """
        # Agrupar por client_id e calcular estatísticas
        clients = df.groupby('client_id').agg({
            'risk_profile': lambda x: x.mode().iloc[0] if not x.mode().empty else x.iloc[0],
            'invested_amount': ['sum', 'count'],
            'investment_name': 'nunique'
        }).reset_index()
        
        # Achatar as colunas multi-level
        clients.columns = ['client_id', 'risk_profile', 'total_invested', 'total_positions', 'unique_investments']
        
        logger.info(f"Clientes únicos extraídos: {len(clients)}")
        logger.info(f"Distribuição de perfis: {clients['risk_profile'].value_counts().to_dict()}")
        
        return clients
    
    def get_unique_investments(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Extrai investimentos únicos do DataFrame
        
        Args:
            df: DataFrame com dados do CSV
            
        Returns:
            DataFrame com investimentos únicos
        """
        investments = df[['investment_name', 'investment_type']].drop_duplicates()
        
        logger.info(f"Investimentos únicos extraídos: {len(investments)}")
        
        return investments
    
    def fetch_random_users_for_clients(self, clients_df: pd.DataFrame, nationality: str = 'br') -> Dict[int, Dict]:
        """
        Busca usuários aleatórios da API para preencher dados dos clientes do CSV
        
        Args:
            clients_df: DataFrame com clientes únicos do CSV
            nationality: Nacionalidade dos usuários
            
        Returns:
            Dicionário mapeando client_id para dados do usuário da API
        """
        client_count = len(clients_df)
        logger.info(f"Buscando {client_count} usuários aleatórios da API para preencher dados dos clientes")
        
        users = []
        batch_size = 5000  # Limite máximo da API
        
        while len(users) < client_count:
            remaining = min(batch_size, client_count - len(users))
            
            try:
                url = f"https://randomuser.me/api/"
                params = {
                    'results': remaining,
                    'nat': nationality,
                    'inc': 'name,email,phone,cell,login,dob,picture'
                }
                
                response = requests.get(url, params=params, timeout=30)
                response.raise_for_status()
                
                data = response.json()
                users.extend(data['results'])
                
                logger.info(f"Buscados {len(users)}/{client_count} usuários")
                
                # Pequena pausa para não sobrecarregar a API
                time.sleep(0.5)
                
            except Exception as e:
                logger.error(f"Erro ao buscar usuários da API: {e}")
                raise
        
        # Mapear client_ids para dados da API
        client_user_mapping = {}
        for i, (_, client) in enumerate(clients_df.iterrows()):
            if i < len(users):
                client_user_mapping[client['client_id']] = users[i]
        
        logger.info(f"Mapeamento criado para {len(client_user_mapping)} clientes")
        return client_user_mapping
    
    def insert_investments(self, investments_df: pd.DataFrame):
        """
        Insere investimentos únicos no banco de dados
        
        Args:
            investments_df: DataFrame com investimentos únicos
        """
        logger.info("Inserindo investimentos no banco de dados")
        
        try:
            for _, investment in investments_df.iterrows():
                # Primeiro verificar se o investimento já existe
                check_query = "SELECT investment_id FROM INVESTMENT WHERE name = %s"
                self.cursor.execute(check_query, (investment['investment_name'],))
                existing = self.cursor.fetchone()
                
                if not existing:
                    insert_query = """
                    INSERT INTO INVESTMENT (name, type) 
                    VALUES (%s, %s)
                    """
                    self.cursor.execute(insert_query, (
                        investment['investment_name'],
                        investment['investment_type']
                    ))
            
            self.connection.commit()
            logger.info(f"Investimentos processados: {len(investments_df)}")
            
        except Exception as e:
            self.connection.rollback()
            logger.error(f"Erro ao inserir investimentos: {e}")
            raise
    
    def get_advisor_count(self) -> int:
        """
        Retorna a quantidade de assessores no banco
        
        Returns:
            Número de assessores
        """
        self.cursor.execute("SELECT COUNT(*) FROM ADVISOR")
        count = self.cursor.fetchone()[0]
        logger.info(f"Assessores encontrados no banco: {count}")
        return count
    
    def ensure_six_advisors(self):
        """Garante que existam 6 assessores no banco sem trigger de redistribuição"""
        try:
            logger.info("Garantindo que existam 6 assessores no sistema")
            
            # Desabilitar trigger temporariamente
            logger.info("Desabilitando trigger de redistribuição...")
            self.cursor.execute("DROP TRIGGER IF EXISTS auto_redistribute_on_new_advisor ON ADVISOR;")
            
            # Criar assessores faltantes
            created_count = 0
            for i in range(1, 7):
                check_query = "SELECT advisor_id FROM ADVISOR WHERE advisor_id = %s"
                self.cursor.execute(check_query, (i,))
                if not self.cursor.fetchone():
                    insert_query = "INSERT INTO ADVISOR (advisor_id, uid) VALUES (%s, %s)"
                    self.cursor.execute(insert_query, (i, f'auth0|advisor{i}'))
                    created_count += 1
            
            self.connection.commit()
            logger.info(f"Assessores criados: {created_count}")
            
            # Recriar trigger
            logger.info("Recriando trigger de redistribuição...")
            self.cursor.execute("""
            CREATE TRIGGER auto_redistribute_on_new_advisor
                AFTER INSERT ON ADVISOR
                FOR EACH ROW
                EXECUTE FUNCTION trigger_redistribution_on_new_advisor();
            """)
            
            # Fazer redistribuição manual se criou assessores
            if created_count > 0:
                logger.info("Executando redistribuição manual...")
                # Temporariamente permitir NULL no client_id só para esta operação
                self.cursor.execute("ALTER TABLE ACTIVITY_LOG ALTER COLUMN client_id DROP NOT NULL;")
                self.cursor.execute("SELECT redistribute_clients_among_advisors();")
                self.cursor.execute("ALTER TABLE ACTIVITY_LOG ALTER COLUMN client_id SET NOT NULL;")
            
            self.connection.commit()
            logger.info("Sistema configurado com 6 assessores")
            
        except Exception as e:
            self.connection.rollback()
            logger.error(f"Erro ao configurar assessores: {e}")
            raise
    
    def insert_clients_with_api_data(self, clients_df: pd.DataFrame, client_user_mapping: Dict[int, Dict]):
        """
        Insere clientes do CSV preenchendo dados faltantes com a API
        
        Args:
            clients_df: DataFrame com clientes únicos do CSV
            client_user_mapping: Mapeamento de client_id para dados da API
        """
        logger.info("Inserindo clientes com dados preenchidos pela API")
        
        # Garantir que temos 6 assessores ANTES de começar
        self.ensure_six_advisors()
        
        advisor_count = self.get_advisor_count()
        if advisor_count == 0:
            raise ValueError("Nenhum assessor encontrado no banco. Insira assessores primeiro.")
        
        try:
            inserted_count = 0
            updated_count = 0
            skipped_count = 0
            
            for _, client in clients_df.iterrows():
                client_id = client['client_id']
                
                if client_id not in client_user_mapping:
                    logger.warning(f"Dados da API não encontrados para cliente {client_id}")
                    skipped_count += 1
                    continue
                
                user_data = client_user_mapping[client_id]
                
                # Extrair dados da API
                full_name = f"{user_data['name']['first']} {user_data['name']['last']}"
                original_email = user_data['email']
                
                # Preferir telefone fixo, senão celular
                phone = user_data.get('phone', user_data.get('cell', ''))
                
                # Distribuir entre os 6 assessores de forma cíclica
                advisor_id = ((client_id - 1) % 6) + 1
                
                # Verificar se cliente já existe pelo ID
                check_query = "SELECT client_id FROM CLIENT WHERE client_id = %s"
                self.cursor.execute(check_query, (client_id,))
                existing = self.cursor.fetchone()
                
                if existing:
                    # Cliente existe - fazer UPDATE (não atualizar email para evitar conflitos)
                    update_query = """
                    UPDATE CLIENT SET
                        name = %s,
                        phone_number = %s,
                        risk_profile_form = %s,
                        advisor_id = %s,
                        updated_at = CURRENT_TIMESTAMP
                    WHERE client_id = %s
                    """
                    self.cursor.execute(update_query, (
                        full_name, phone, client['risk_profile'], advisor_id, client_id
                    ))
                    updated_count += 1
                else:
                    # Cliente não existe - verificar se email já existe
                    email_check_query = "SELECT client_id FROM CLIENT WHERE email = %s"
                    self.cursor.execute(email_check_query, (original_email,))
                    email_exists = self.cursor.fetchone()
                    
                    if email_exists:
                        # Email já existe - gerar email único
                        email = f"client_{client_id}_{original_email}"
                    else:
                        email = original_email
                    
                    # Fazer INSERT
                    insert_query = """
                    INSERT INTO CLIENT (
                        client_id, name, email, phone_number, 
                        risk_profile_form, risk_profile_wallet, compliance, advisor_id
                    )
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
                    """
                    self.cursor.execute(insert_query, (
                        client_id, full_name, email, phone,
                        client['risk_profile'], 'Conservador', False, advisor_id
                    ))
                    inserted_count += 1
            
            self.connection.commit()
            logger.info(f"Clientes inseridos: {inserted_count}, atualizados: {updated_count}, ignorados: {skipped_count}")
            
        except Exception as e:
            self.connection.rollback()
            logger.error(f"Erro ao inserir clientes: {e}")
            raise
    
    def insert_client_investments(self, df: pd.DataFrame):
        """
        Insere os investimentos dos clientes baseado nos dados do CSV
        
        Args:
            df: DataFrame original com todos os dados do CSV
        """
        logger.info("Inserindo investimentos dos clientes baseado no CSV")
        
        try:
            # Desabilitar triggers temporariamente para evitar problemas de validação
            logger.info("Desabilitando triggers temporariamente...")
            self.cursor.execute("SET session_replication_role = replica;")
            
            # Buscar IDs dos investimentos
            investment_ids = {}
            self.cursor.execute("SELECT investment_id, name FROM INVESTMENT")
            for inv_id, name in self.cursor.fetchall():
                investment_ids[name] = inv_id
            
            inserted_count = 0
            updated_count = 0
            skipped_count = 0
            
            # Limite máximo para integer no PostgreSQL (32-bit signed)
            MAX_INT = 2147483647
            
            # Inserir investimentos dos clientes
            for _, row in df.iterrows():
                if row['investment_name'] not in investment_ids:
                    logger.warning(f"Investimento não encontrado: {row['investment_name']}")
                    skipped_count += 1
                    continue
                
                # Converter valores para absolutos e limitar ao máximo do PostgreSQL
                try:
                    quantity = min(abs(int(float(row['quantity']))), MAX_INT)
                    invested_amount = abs(float(row['invested_amount']))
                except (ValueError, OverflowError):
                    logger.warning(f"Valores inválidos para cliente {row['client_id']}: quantity={row['quantity']}, amount={row['invested_amount']}")
                    skipped_count += 1
                    continue
                
                # Pular se quantidade for zero ou muito pequena
                if quantity <= 0 or invested_amount <= 0.01:
                    skipped_count += 1
                    continue
                
                # Gerar data aleatória nos últimos 2 anos
                start_date = datetime.now() - timedelta(days=730)
                end_date = datetime.now()
                random_date = start_date + timedelta(
                    seconds=random.randint(0, int((end_date - start_date).total_seconds()))
                )
                
                # Verificar se já existe este investimento para o cliente
                check_query = """
                SELECT quantity, invested_amount 
                FROM CLIENT_INVESTMENT 
                WHERE client_id = %s AND investment_id = %s
                """
                self.cursor.execute(check_query, (row['client_id'], investment_ids[row['investment_name']]))
                existing = self.cursor.fetchone()
                
                if existing:
                    # Converter Decimal para float para compatibilidade
                    existing_quantity = int(existing[0])
                    existing_amount = float(existing[1])
                    
                    # Verificar se a soma não excederá o limite
                    new_quantity = min(existing_quantity + quantity, MAX_INT)
                    new_amount = existing_amount + invested_amount
                    
                    # Existe - fazer UPDATE somando valores
                    update_query = """
                    UPDATE CLIENT_INVESTMENT SET
                        quantity = %s,
                        invested_amount = %s,
                        investment_date = %s,
                        updated_at = CURRENT_TIMESTAMP
                    WHERE client_id = %s AND investment_id = %s
                    """
                    self.cursor.execute(update_query, (
                        new_quantity, new_amount, random_date,
                        row['client_id'], investment_ids[row['investment_name']]
                    ))
                    updated_count += 1
                else:
                    # Não existe - fazer INSERT
                    insert_query = """
                    INSERT INTO CLIENT_INVESTMENT (
                        client_id, investment_id, investment_date, quantity, invested_amount
                    )
                    VALUES (%s, %s, %s, %s, %s)
                    """
                    self.cursor.execute(insert_query, (
                        row['client_id'], investment_ids[row['investment_name']],
                        random_date, quantity, invested_amount
                    ))
                    inserted_count += 1
            
            # Reabilitar triggers
            logger.info("Reabilitando triggers...")
            self.cursor.execute("SET session_replication_role = DEFAULT;")
            
            self.connection.commit()
            logger.info(f"Investimentos dos clientes inseridos: {inserted_count}, atualizados: {updated_count}, ignorados: {skipped_count}")
            
        except Exception as e:
            # Garantir que os triggers sejam reabilitados mesmo em caso de erro
            try:
                self.cursor.execute("SET session_replication_role = DEFAULT;")
                self.connection.rollback()
            except:
                pass
            logger.error(f"Erro ao inserir investimentos dos clientes: {e}")
            raise
    
    def populate_database_from_csv_and_api(self, csv_path: str):
        """
        Executa o processo completo de população do banco usando CSV + API
        
        Args:
            csv_path: Caminho para o arquivo CSV
        """
        try:
            self.connect_database()
            
            # 1. Carregar e processar dados do CSV
            logger.info("=== FASE 1: Processando dados do CSV ===")
            df = self.load_csv_data(csv_path)
            
            # 2. Extrair clientes únicos com perfis
            logger.info("=== FASE 2: Extraindo clientes únicos ===")
            clients_df = self.get_unique_clients_with_profile(df)
            
            # 3. Extrair investimentos únicos
            logger.info("=== FASE 3: Extraindo investimentos únicos ===")
            investments_df = self.get_unique_investments(df)
            
            # 4. Buscar dados da API para preencher informações dos clientes
            logger.info("=== FASE 4: Buscando dados da API ===")
            client_user_mapping = self.fetch_random_users_for_clients(clients_df)
            
            # 5. Inserir dados no banco
            logger.info("=== FASE 5: Populando banco de dados ===")
            self.insert_investments(investments_df)
            self.insert_clients_with_api_data(clients_df, client_user_mapping)
            self.insert_client_investments(df)
            
            logger.info("=== POPULAÇÃO CONCLUÍDA COM SUCESSO! ===")
            
            # 6. Estatísticas finais
            self.print_final_statistics()
            
        except Exception as e:
            logger.error(f"Erro durante população do banco: {e}")
            raise
        finally:
            self.disconnect_database()
    
    def print_final_statistics(self):
        """Imprime estatísticas finais da população"""
        try:
            # Contar registros inseridos
            self.cursor.execute("SELECT COUNT(*) FROM CLIENT")
            client_count = self.cursor.fetchone()[0]
            
            self.cursor.execute("SELECT COUNT(*) FROM INVESTMENT")
            investment_count = self.cursor.fetchone()[0]
            
            self.cursor.execute("SELECT COUNT(*) FROM CLIENT_INVESTMENT")
            position_count = self.cursor.fetchone()[0]
            
            self.cursor.execute("SELECT COUNT(*) FROM ADVISOR")
            advisor_count = self.cursor.fetchone()[0]
            
            # Estatísticas de compliance
            self.cursor.execute("SELECT COUNT(*) FROM CLIENT WHERE compliance = FALSE")
            non_compliant_count = self.cursor.fetchone()[0]
            
            # Estatísticas de distribuição entre assessores
            self.cursor.execute("""
                SELECT advisor_id, COUNT(*) as client_count
                FROM CLIENT 
                GROUP BY advisor_id 
                ORDER BY advisor_id
            """)
            advisor_distribution = self.cursor.fetchall()
            
            logger.info("=== ESTATÍSTICAS FINAIS ===")
            logger.info(f"Assessores: {advisor_count}")
            logger.info(f"Clientes: {client_count}")
            logger.info(f"Investimentos: {investment_count}")
            logger.info(f"Posições: {position_count}")
            logger.info(f"Clientes não conformes: {non_compliant_count}")
            
            logger.info("=== DISTRIBUIÇÃO ENTRE ASSESSORES ===")
            for advisor_id, client_count in advisor_distribution:
                logger.info(f"Assessor {advisor_id}: {client_count} clientes")
            
        except Exception as e:
            logger.error(f"Erro ao gerar estatísticas: {e}")


def main():
    """Função principal para executar a população do banco"""
    
    # Configuração do banco de dados
    try:
        with open('db_config.json', 'r') as f:
            db_config = json.load(f)
    except FileNotFoundError:
        print("❌ Arquivo db_config.json não encontrado!")
        print("Execute setup_and_populate.py primeiro para criar a configuração.")
        return 1
    except Exception as e:
        print(f"❌ Erro ao carregar configuração: {e}")
        return 1
    
    # Caminho para o arquivo CSV (ajustado para a estrutura do projeto)
    csv_path = '../../dataset/PosicaoIntelli.csv'
    
    # Executar população
    populator = DatabasePopulator(db_config)
    populator.populate_database_from_csv_and_api(csv_path)


if __name__ == "__main__":
    main()