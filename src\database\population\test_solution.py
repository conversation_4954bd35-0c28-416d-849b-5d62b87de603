#!/usr/bin/env python3
"""
Script de teste e validação da solução de população do banco de dados
"""

import pandas as pd
import requests
import psycopg2
import json
import sys
from pathlib import Path
import logging

# Configuração de logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class SolutionTester:
    def __init__(self):
        """Inicializa o testador da solução"""
        self.db_config = None
        self.connection = None
        self.cursor = None
        
    def load_config(self):
        """Carrega configuração do banco de dados"""
        try:
            config_path = Path('db_config.json')
            if not config_path.exists():
                logger.error("❌ Arquivo db_config.json não encontrado!")
                return False
                
            with open(config_path, 'r') as f:
                self.db_config = json.load(f)
            logger.info("✅ Configuração carregada com sucesso")
            return True
        except Exception as e:
            logger.error(f"❌ Erro ao carregar configuração: {e}")
            return False
    
    def connect_database(self):
        """Conecta ao banco de dados"""
        try:
            self.connection = psycopg2.connect(**self.db_config)
            self.cursor = self.connection.cursor()
            logger.info("✅ Conexão com banco estabelecida")
            return True
        except Exception as e:
            logger.error(f"❌ Erro ao conectar com banco: {e}")
            return False
    
    def disconnect_database(self):
        """Desconecta do banco de dados"""
        if self.cursor:
            self.cursor.close()
        if self.connection:
            self.connection.close()
        logger.info("🔌 Conexão com banco encerrada")
    
    def test_csv_loading(self):
        """Testa carregamento do arquivo CSV"""
        logger.info("=== TESTE 1: Carregamento do CSV ===")
        
        # CAMINHO CORRIGIDO para estrutura do projeto
        csv_path = Path('../../dataset/PosicaoIntelli.csv')
        
        try:
            if not csv_path.exists():
                logger.error(f"❌ Arquivo CSV não encontrado: {csv_path}")
                logger.info("📁 Verifique se o arquivo está em src/dataset/PosicaoIntelli.csv")
                return False
            
            # Tentar carregar o CSV
            column_names = ['client_id', 'invested_amount', 'quantity', 'investment_name', 'investment_type', 'risk_profile']
            df = pd.read_csv(csv_path, names=column_names, encoding='utf-8-sig')
            
            # Validar dados
            total_records = len(df)
            unique_clients = df['client_id'].nunique()
            
            logger.info(f"✅ CSV carregado com sucesso!")
            logger.info(f"📊 Total de registros: {total_records}")
            logger.info(f"👥 Clientes únicos: {unique_clients}")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Erro no teste de CSV: {e}")
            return False
    
    def test_api_connectivity(self):
        """Testa conectividade com a API randomuser.me"""
        logger.info("=== TESTE 2: Conectividade com API ===")
        
        try:
            url = "https://randomuser.me/api/"
            params = {
                'results': 1,
                'nat': 'br',
                'inc': 'name,email,phone'
            }
            
            response = requests.get(url, params=params, timeout=10)
            response.raise_for_status()
            
            data = response.json()
            user = data['results'][0]
            
            logger.info("✅ API randomuser.me acessível")
            logger.info(f"👤 Exemplo de usuário: {user['name']['first']} {user['name']['last']}")
            logger.info(f"📧 Email: {user['email']}")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Erro ao acessar API: {e}")
            return False
    
    def test_database_population(self):
        """Testa se o banco foi populado corretamente"""
        logger.info("=== TESTE 3: População do Banco ===")
        
        try:
            # Testar tabelas principais
            tables_to_check = [
                ('CLIENT', 'Clientes'),
                ('INVESTMENT', 'Investimentos'),
                ('CLIENT_INVESTMENT', 'Posições'),
                ('ADVISOR', 'Assessores')
            ]
            
            results = {}
            
            for table, description in tables_to_check:
                self.cursor.execute(f"SELECT COUNT(*) FROM {table}")
                count = self.cursor.fetchone()[0]
                results[table] = count
                logger.info(f"📊 {description}: {count} registros")
            
            # Validações específicas
            if results['CLIENT'] == 0:
                logger.error("❌ Nenhum cliente encontrado!")
                return False
            
            if results['ADVISOR'] == 0:
                logger.error("❌ Nenhum assessor encontrado!")
                return False
            
            logger.info("✅ Banco populado com sucesso!")
            return True
            
        except Exception as e:
            logger.error(f"❌ Erro ao verificar população: {e}")
            return False
    
    def test_api_data_integration(self):
        """Testa se dados da API foram integrados"""
        logger.info("=== TESTE 4: Integração de Dados da API ===")
        
        try:
            # Verificar se clientes têm dados da API
            self.cursor.execute("""
                SELECT 
                    COUNT(*) as total,
                    COUNT(CASE WHEN name IS NOT NULL AND name != '' THEN 1 END) as com_nome,
                    COUNT(CASE WHEN email IS NOT NULL AND email != '' THEN 1 END) as com_email,
                    COUNT(CASE WHEN phone_number IS NOT NULL AND phone_number != '' THEN 1 END) as com_telefone
                FROM CLIENT
            """)
            
            result = self.cursor.fetchone()
            total, com_nome, com_email, com_telefone = result
            
            logger.info(f"👥 Total de clientes: {total}")
            logger.info(f"📝 Com nome: {com_nome} ({(com_nome/total*100):.1f}%)")
            logger.info(f"📧 Com email: {com_email} ({(com_email/total*100):.1f}%)")
            logger.info(f"📞 Com telefone: {com_telefone} ({(com_telefone/total*100):.1f}%)")
            
            # Mostrar exemplos
            self.cursor.execute("""
                SELECT client_id, name, email, phone_number 
                FROM CLIENT 
                WHERE name IS NOT NULL 
                LIMIT 3
            """)
            
            examples = self.cursor.fetchall()
            logger.info("👤 Exemplos de clientes:")
            for client_id, name, email, phone in examples:
                logger.info(f"   ID: {client_id} | Nome: {name} | Email: {email}")
            
            if com_nome > 0 and com_email > 0:
                logger.info("✅ Dados da API integrados com sucesso!")
                return True
            else:
                logger.error("❌ Dados da API não foram integrados!")
                return False
                
        except Exception as e:
            logger.error(f"❌ Erro ao verificar integração da API: {e}")
            return False
    
    def test_client_distribution(self):
        """Testa distribuição de clientes entre assessores"""
        logger.info("=== TESTE 5: Distribuição entre Assessores ===")
        
        try:
            self.cursor.execute("""
                SELECT 
                    advisor_id,
                    COUNT(*) as quantidade_clientes,
                    ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM CLIENT), 2) as percentual
                FROM CLIENT 
                GROUP BY advisor_id
                ORDER BY advisor_id
            """)
            
            distribution = self.cursor.fetchall()
            
            logger.info("📊 Distribuição de clientes por assessor:")
            for advisor_id, quantidade, percentual in distribution:
                logger.info(f"   Assessor {advisor_id}: {quantidade} clientes ({percentual}%)")
            
            # Verificar se distribuição está balanceada
            quantities = [q[1] for q in distribution]
            if len(quantities) > 1:
                max_diff = max(quantities) - min(quantities)
                avg_clients = sum(quantities) / len(quantities)
                balance_ratio = max_diff / avg_clients
                
                if balance_ratio < 0.2:  # Diferença menor que 20%
                    logger.info("✅ Distribuição bem balanceada!")
                else:
                    logger.warning("⚠️ Distribuição pode estar desbalanceada")
            
            logger.info("✅ Teste de distribuição concluído!")
            return True
            
        except Exception as e:
            logger.error(f"❌ Erro ao verificar distribuição: {e}")
            return False
    
    def test_compliance_calculation(self):
        """Testa cálculo de compliance"""
        logger.info("=== TESTE 6: Cálculo de Compliance ===")
        
        try:
            self.cursor.execute("""
                SELECT 
                    COUNT(*) as total,
                    COUNT(CASE WHEN compliance = TRUE THEN 1 END) as conformes,
                    COUNT(CASE WHEN compliance = FALSE THEN 1 END) as nao_conformes
                FROM CLIENT
            """)
            
            result = self.cursor.fetchone()
            total, conformes, nao_conformes = result
            
            logger.info(f"📊 Compliance dos clientes:")
            logger.info(f"   Total: {total}")
            logger.info(f"   Conformes: {conformes} ({(conformes/total*100):.1f}%)")
            logger.info(f"   Não conformes: {nao_conformes} ({(nao_conformes/total*100):.1f}%)")
            
            logger.info("✅ Compliance calculado!")
            return True
            
        except Exception as e:
            logger.error(f"❌ Erro ao verificar compliance: {e}")
            return False
    
    def run_all_tests(self):
        """Executa todos os testes"""
        logger.info("🧪 INICIANDO BATERIA DE TESTES")
        logger.info("=" * 50)
        
        tests = [
            ("Carregamento de Configuração", self.load_config),
            ("Carregamento do CSV", self.test_csv_loading),
            ("Conectividade com API", self.test_api_connectivity),
            ("Conexão com Banco", self.connect_database),
            ("População do Banco", self.test_database_population),
            ("Integração de Dados da API", self.test_api_data_integration),
            ("Distribuição entre Assessores", self.test_client_distribution),
            ("Cálculo de Compliance", self.test_compliance_calculation)
        ]
        
        passed = 0
        failed = 0
        
        for test_name, test_func in tests:
            try:
                if test_func():
                    passed += 1
                else:
                    failed += 1
            except Exception as e:
                logger.error(f"❌ Erro no teste '{test_name}': {e}")
                failed += 1
        
        # Desconectar do banco
        self.disconnect_database()
        
        # Relatório final
        logger.info("=" * 50)
        logger.info("📋 RELATÓRIO FINAL DOS TESTES")
        logger.info(f"✅ Testes aprovados: {passed}")
        logger.info(f"❌ Testes falharam: {failed}")
        logger.info(f"📊 Taxa de sucesso: {(passed/(passed+failed)*100):.1f}%")
        
        if failed == 0:
            logger.info("🎉 TODOS OS TESTES PASSARAM! Solução funcionando perfeitamente!")
        else:
            logger.warning("⚠️ Alguns testes falharam. Verifique os erros acima.")
        
        return failed == 0


def main():
    """Função principal"""
    tester = SolutionTester()
    success = tester.run_all_tests()
    
    if success:
        sys.exit(0)
    else:
        sys.exit(1)


if __name__ == "__main__":
    main()

