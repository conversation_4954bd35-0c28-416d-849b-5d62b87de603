<div align="center">
  <a href="https://www.inteli.edu.br/">
    <img src="../../docs/assets/logo_inteli_btg.png" 
         alt="Imagem contendo a logotipo do Banco BTG Pactual e do Inteli" 
         border="0" 
         style="max-width: 600px; width: 100%; height: auto;">
  </a>
</div>

<br/>
<br/>

# Nome do Projeto: Pati (Plataforma de Adequação de Tipo de Investidor)

## Nome do Grupo: MMs (Meninas Malvadas)

## Visão Geral

&emsp; Este diretório contém os scripts SQL para criação e inserção de dados em um banco de dados PostgreSQL, voltado à solução que gerencia usuários administrativos, registros de uso, profissionais, atendimentos e tecnologias assistivas. O banco de dados foi modelado com foco em normalização e integridade referencial.

> Novos scripts poderão ser adicionados futuramente nesta pasta `~/src/database`.

## Estrutura de Diretórios

```text
src/
└── database/
    ├── 01_create_database.sql   # Criação de tabelas e estrutura do banco
    ├── 02_initial_data.sql      # Dados fictícios para testes e desenvolvimento
    └── index.md                 # Documentação e instruções de uso
    └── .env                     # Arquivo com credenciais de acesso (não versionado)
```

## Como Executar os Scripts

### Pré-requisitos
- PostgreSQL instalado ou acesso a um serviço compatível (local ou remoto)
- Ambiente de execução SQL como:
  - Terminal PostgreSQL (`psql`)
  - Leitores/clients de SGDB como [DBeaver](https://dbeaver.io/)
  - Serviços de nuvem como [Amazon RDS](https://aws.amazon.com/rds/), [Supabase](https://supabase.com/), entre outros

### Informações sobre configuração, execução e implantação

&emsp;As informações detalhadas sobre configuração, execução e implantação do banco de dados estão disponível na seção `10.1 Implantação e Configuração do Banco de Dados`, disponível no documento **[project.md](../../docs/project.md)**.

### Análise de Performance de Consultas SQL com explain analyze

&emsp; Essa seção tem o objetivo de documentar o processo de análise de complexidade das consultas SQL mais críticas da aplicação, sobretudo, as pertencentes ao serviço `api_data_presentation`, o qual irá realizar consultas frequentes ao banco de dados para fornecer informações à camada de visualização (front-end mobile).<br/>
&emsp;Nesse sentido, foi usada a interface gráfica **DBeaver** para executar as consultas selecionadas com o **EXPLAIN ANALYZER**; os resultados foram registrados em um documento que pode ser acessado neste **<a href="https://docs.google.com/document/d/17-QW8YIe1i-AAhYXuQZ7JnNRzcHVBdPwYq4_AkWljc8/edit?usp=sharing">LINK PÚBLICO</a>** - vale ressaltar que todas as consultas SQL foram indicadas nas respectivas funções às quais pertencem dentro do back-end da aplicação.

#### Consulta 1: Estatísticas de Compliance por Advisor

**Análise**: A consulta agrega dados da tabela `client` filtrando por `advisor_id`. O plano de execução utiliza **Index Scan** no índice `idx_client_advisor`, o que é ideal para a cláusula `WHERE`. A agregação (`SUM`) é realizada sobre o resultado já filtrado.

**Performance**: O tempo de execução de **0.136 ms** é satisfatório.

**Conclusão**: Consulta otimizada. Nenhum gargalo identificado.

---
#### Consulta 2: Lista de Clientes por Advisor

**Análise**: A consulta busca clientes de um advisor, com filtros dinâmicos e agregação para calcular o total investido. O plano utiliza **Index Scan** em `idx_client_advisor`, o que é eficiente para o filtro principal `c.advisor_id`. O `LEFT JOIN` com `client_investment` não foi executado (`never executed`) porque a busca inicial na tabela `client` não retornou resultados (`rows=0`).

**Performance**: O tempo de execução de **0.232 ms** é baixo.

**Possível Gargalo**: A análise é inconclusiva sobre o desempenho do `JOIN` e `GROUP BY` sob carga, pois nenhum dado foi processado (`rows=0`). O desempenho com um advisor que possui muitos clientes e investimentos precisa ser validado.

---
#### Consulta 3: Busca de Advisor por UID

**Análise**: Realiza uma busca por um registro único na tabela `advisor` pela coluna `uid`. O plano de execução mostra um **Index Scan** utilizando a chave única `advisor_uid_key`, que é o método mais eficiente possível.

**Performance**: O tempo de execução de **0.181 ms** é satisfatório.

**Conclusão**: Consulta otimizada. Nenhum gargalo identificado.

---
#### Consulta 4: Criação de Advisor

**Análise**: A consulta realiza um `INSERT` simples na tabela `advisor`. O plano de execução reflete esta operação direta.

**Performance**: O tempo de execução de **0.121 ms** é satisfatório para uma operação de escrita.

**Conclusão**: Operação eficiente. Nenhuma otimização necessária.

---
#### Consulta 5: Lista de Investimentos por Cliente

**Análise**: Busca investimentos de um cliente, com filtros dinâmicos e ordenação. O plano utiliza **Index Scan** em ambas as tabelas (`investment` e `client_investment`), o que é ótimo.

**Performance**: O tempo de execução de **0.078 ms** é satisfatório.

**Possível Gargalo**: O plano foi gerado para uma consulta que não retornou dados (`rows=0`). A etapa de **Sort** pela chave `ci.investment_date` é um gargalo em potencial para clientes com muitos investimentos.

**Otimização Recomendada**: Dessa forma, a criação de um índice composto poderia eliminar a necessidade da operação de Sort, otimizando a performance da consulta:

```sql
CREATE INDEX idx_client_investment_client_date ON client_investment (client_id, investment_date);
```
---
#### Consulta 6: Busca de Cliente por ID

**Análise**: Busca um cliente pelo seu **client_id**. O plano mostra um **Index Scan** na chave primária **client_pkey**, a forma mais rápida de acessar um único registro.

**Performance**: O tempo de execução de **0.125 ms** é satisfatório.

**Conclusão**: Consulta otimizada. Nenhum gargalo identificado.

---
#### Consulta 7: Busca de Investimento por ID

**Análise**: Busca um investimento pelo seu **investment_id**. O plano utiliza a chave primária **investment_pkey** via **Index Scan**.

**Performance**: O tempo de execução de **0.029 ms** é satisfatório.

**Conclusão**: Consulta otimizada. Nenhum gargalo identificado.

&emsp;Por fim, as oportunidades de melhoria identificadas com a ferramenta EXPLAIN ANALYZER já foram integrados aos scripts SQL usados na aplicação (`01_create_database.sql` e `02_initial_data.sql`). 

### Documentação dos testes de cargas no banco de dados

&emsp;Essa seção tem o propósito de documentar a condução dos testes de carga no banco de dados relacional da aplicação (PostgreSQL). Conforme o RNF#05 de escalibilidade, que preve que a aplicação suporte o acesso simultâneo de 500 usuários sem degradação perceptiva de performance, os testes realizados tiveram o objetivo de avaliar a viabilidade do cumprimento desse requisito não funcional, considerando aspectos técnicos, de recursos e de prazo.
&emsp;A ferramenta utilizada para realizar os testes de carga no banco de dados foi o pacote `pgbench` nativo do PostgreSQL, que habilita criação de banco de dados de teste, população com alto volume de dados e condução de testes de carga de trabalho. 

#### Contexto do Projeto e Requisitos de Desempenho

**Arquitetura**: Backend em arquitetura SOA, com 3 serviços (.NET 8/9).
**Conectividade no Banco de Dados**: Npgsql (Driver PGSQL), utilizando Connection Pooling (com número máximo de 100 conexões simultâneas).
**RNF#05 (Escalabilidade)**: "O sistema deve suportar ao menos 500 usuários simultâneos sem perda perceptível de desempenho."
**Contexto de máxima sobrecarga do banco de dados**: Dado o uso de Connection Pooling no Npgsql (Max Pool Size padrão de 100 conexões por serviço), o estresse máximo simulado no banco de dados, em termos de conexões físicas dos serviços, é de aproximadamente 300 conexões simultâneas (3 serviços x 100 conexões/serviço). O teste de carga no DB visa validar a performance máxima do banco de dados, visando atingir este cenário.

#### Ambiente de Teste

**Instância PostgreSQL**: Container Docker
- **Nome do Container Docker**: src-db-1
- **Versão do PostgreSQL no Container**: 16.9
- **Host de Conexão**: localhost
- **Porta de Conexão**: 15432
- **Banco de Dados Testado**: mean_girls
- **Usuário de Conexão**: inteli

**Configuração de Hardware (Host Docker)**:
- **CPU**: 12 Cores, Intel Core i5-1335U
- **Memória**: 16 GB RAM
- **Disco**: 256 GB SSD
**Recursos Alocados ao Container (docker stats)**:
- **CPU Limit**: Não especificado/Ilimitado
- **Memory Limit**: 15.31 GiB

**Observação**: O parâmetro `max_connections` do postgresql não foi alterado, logo, o número máximo de conexões da instância se manteve como o valor default (100).

#### Metodologia do Teste de Carga

**Ferramenta Utilizada**: `pgbench` (nativa do PostgreSQL), o pacote instalado foi `sudo apt install postgresql-contrib-16` em uma máquina Linux Ubuntu 24.04.

**Propósito do pgbench**: Gerar carga direta no banco de dados para simular o comportamento de múltiplos clientes/conexões (representando os pools de conexão dos serviços backend) e medir o desempenho do DB (TPS, latência, uso de recursos).

**População da Base de Dados**:
- **Método**: 
```bash
pgbench -i -s [scale_factor]
```

- **Scale Factor Utilizado**: 60 (6 milhões de registros/linhas)

**Justificativa**: Cria uma base de dados com volume controlado para o cenário de teste padrão do pgbench, permitindo mensuração de desempenho.

**Cenários de Carga Executados (Ramp-up)**:
- **Cenário de Teste**: Transações padrão do pgbench (mix de SELECT/UPDATE/INSERT/DELETE, simulando operações transacionais).
- **Parâmetros Comuns**: Threads (-j) = 8, Duração (-T) = 300 segundos (5 minutos).
- **Nível de Concorrência (-c)**: 10, 30, 50, 75, 97.
- **Monitoramento**: docker stats (CPU, Memória, I/O do Container) e logs do PostgreSQL.

#### Resultados Consolidados do Teste de Carga

| Teste | Clientes (`-c`) | TPS (transações/seg) | Latência Média (ms) | Transações Processadas | Falhas (%) |
| :---- | :-------------- | :------------------- | :------------------ | :--------------------- | :--------- |
| 1     | 10              | 2215.44              | 4.514               | 664,382                 | 0.00%      |
| 2     | 30              | 4986.68              | 6.016               | 1,495,103                | 0.00%      |
| 3     | 50              | 6121.40              | 8.168               | 1,834,956                | 0.00%      |
| 4     | 75              | 6525.12              | 11.494              | 1,955,818                | 0.00%      |
| 5     | 97              | 6622.11              | 14.648              | 1,983,593                | 0.00%      |

#### Análise dos Resultados

O objetivo inicial do teste de carga era avaliar a viabilidade de suportar 500 usuários simultâneos sem degradação perceptível de performance, considerando que os três serviços backend (com Npgsql, Max Pool Size padrão de 100) gerariam um estresse máximo de 300 conexões simultâneas ao banco de dados. Os testes executados foram até 97 clientes (`pgbench`), que representa um terço da carga máxima esperada dos pools de conexão (300 clientes) - considerando também que esse é o limite de conexões padrões do PostgreSQL.

* **Transações Por Segundo (TPS):**
    * Houve um aumento significativo do TPS com o número de clientes até 50, passando de 2215.44 para 6121.40 TPS.
    * No entanto, o crescimento do TPS se tornou marginal a partir de 50 clientes. Entre 50 e 97 clientes, o TPS aumentou apenas de 6121.40 para 6622.11, indicando que o sistema de banco de dados (ou o hardware do host Docker) está se aproximando de seu ponto de saturação de throughput.
* **Latência Média:**
    * A latência média por transação apresentou um aumento proporcional ao número de clientes. Iniciando em 4.514 ms para 10 clientes, ela cresceu para 14.648 ms com 97 clientes.
    * Embora 14.648 ms ainda seja uma latência relativamente baixa para transações de banco de dados, o aumento de mais de 3x em relação à carga inicial (10 clientes) é um indicativo claro de que o sistema está trabalhando sob maior estresse e que a "perda perceptível de desempenho" para os usuários de backend (e consequentemente para os usuários finais HTTP) pode começar a se manifestar em cargas maiores.
* **Taxa de Falhas:**
    * Todos os testes apresentaram 0% de transações falhas, o que é um resultado excelente. Isso indica que, até o nível de 97 clientes, o banco de dados conseguiu processar todas as requisições sem erros, apenas com aumento na latência.

Com base nos testes realizados até 97 clientes, o banco de dados está **se aproximando de seu limite de desempenho** em relação à CPU disponível/alocada. Embora ainda não tenhamos testado o cenário de 300 conexões (o limite inferido dos pools do backend), a curva de TPS em platô e o aumento da latência já apontam que o sistema não conseguiria escalar linearmente até 300 clientes com a configuração atual sem uma degradação significativa.

O sistema demonstra robustez ao não apresentar falhas, mas a performance (latência e throughput) já mostra sinais de estresse em um terço da carga máxima esperada dos pools de conexão. Para atender ao RNF de 500 usuários sem degradação perceptível, a otimização de recursos ou a investigação de gargalos de CPU é necessária antes de atingir a carga total.

#### Evolução do Script SQL de Criação

# Documentação das Alterações no Script SQL

Durante a atualização do script de banco de dados, foram realizadas mudanças significativas na modelagem, especialmente nas tabelas de auditoria e na forma de controle de compliance. As alterações visaram simplificar a estrutura, melhorar a performance e tornar o sistema mais aderente às necessidades atuais do projeto.

A tabela `INVESTMENT` sofreu uma alteração importante, com a remoção da coluna `risk`, que anteriormente armazenava o nível de risco de cada investimento. Essa decisão está alinhada com a simplificação das regras de negócio, retirando a validação de compatibilidade entre investimentos e perfis de risco dos clientes.

A tabela `ACTIVITY_LOG` foi completamente reformulada. A estrutura anterior, que incluía colunas como `table_name`, `row_id`, `operation_type`, `performed_by`, `old_data` e `new_data`, foi substituída por um modelo mais simples e direto. Agora, o log possui os campos `client_id`, `investment_id` (opcional), `action`, `details` e `created_at`. Essa mudança reduz a complexidade da auditoria e torna os registros mais objetivos e focados nas ações realmente relevantes para o negócio.

Além disso, a tabela `COMPLIANCE_HISTORY` teve apenas uma alteração no nome, que passou de caixa alta (`COMPLIANCE_HISTORY`) para minúscula (`compliance_history`), padronizando a nomenclatura das tabelas no banco de dados.

No que se refere aos índices, foram removidos os índices que anteriormente davam suporte à estrutura antiga de auditoria, como aqueles baseados em `table_name` e `row_id`. Em contrapartida, foi criado um índice composto `idx_client_investment_client_date` na tabela `client_investment`, para otimizar consultas que envolvem `client_id` e `investment_date`. Também foi adicionado o índice `idx_activity_log_client` na tabela `ACTIVITY_LOG`, focando em acelerar consultas por cliente.

As funções de auditoria genérica, como `audit_dml_operations()`, e seus respectivos triggers foram completamente removidos. No lugar, foi criada uma função mais simples chamada `log_activity()`, que permite registrar de forma direta as ações relevantes, como mudanças de compliance ou operações específicas nos investimentos. Essa função é acompanhada de um novo trigger `log_client_compliance_changes`, responsável por monitorar alterações no campo `compliance` da tabela `CLIENT` e registrar os eventos no log.

Outra mudança relevante foi a remoção da função `is_investment_compatible_with_profile()` e de toda a lógica relacionada à validação de compatibilidade entre os perfis de risco dos clientes e os investimentos. Isso reflete uma simplificação das regras de negócio, priorizando a rastreabilidade de compliance, sem impedir tecnicamente a realização de investimentos, mesmo que fora do perfil.

Apesar dessas remoções, foram mantidas funcionalidades essenciais, como a lógica de controle de compliance por meio dos triggers `check_client_compliance` e `track_client_compliance_changes`, além da view `vw_non_compliant_clients`, que permite consultar todos os clientes que estão em situação de não conformidade. A procedure `prepare_normalized_investment_data()` também foi mantida, garantindo suporte a análises baseadas em dados normalizados.

Por fim, as alterações implementadas tornaram o modelo mais eficiente e focado nos aspectos mais críticos para o acompanhamento dos clientes e da operação do sistema, mantendo rastreabilidade suficiente sem sobrecarregar o banco de dados com dados de auditoria excessivos.

#### Atualização do Dicionário de dados

Foram feitas atualizações importantes no modelo de dados para melhorar a auditoria, o controle de alterações e a performance do sistema.

A tabela **`ACTIVITY_LOG`** foi totalmente reformulada. Antes, ela registrava apenas ações específicas sobre clientes e investimentos. Agora, passou a registrar qualquer operação de **INSERT**, **UPDATE** ou **DELETE** em qualquer tabela principal do sistema. Foram adicionados os campos `table_name`, `row_id`, `operation_type`, `performed_by`, `old_data`, `new_data` e `