<div align="center">
  <a href="https://www.inteli.edu.br/">
    <img src="./assets/logo_inteli_btg.png" 
         alt="Imagem contendo a logotipo do Banco BTG Pactual e do Inteli" 
         border="0" 
         style="max-width: 600px; width: 100%; height: auto;">
  </a>
</div>

<br/>

# Nome do Projeto: Pati (Plataforma de Adequação de Tipo de Investidor)

## Nome do Grupo: MMs (Meninas Malvadas)

## Integrantes:

<div align="center">
<table>
  <tr>
    <td align="center">
      <a href="https://www.linkedin.com/in/larissa-temoteo/">
        <img src="https://media.licdn.com/dms/image/v2/D4D03AQE-yMLLUD04Qg/profile-displayphoto-shrink_200_200/profile-displayphoto-shrink_200_200/0/1730056625827?e=1750896000&v=beta&t=Vxukmk-nWK9EjbZGD4zQ0IIi5se0JwECJLmyqZ-2mrg" width="100px;" alt="Foto de Larissa Temoteo" style="border-radius:50%"/>
        <br />
        <b>Larissa Temoteo</b>
      </a>
      <br />
      <a href="https://github.com/larissatemoteo">
        <img src="https://img.shields.io/badge/GitHub-%23121011.svg?logo=github&logoColor=white)" alt="GitHub" height="25"/>
      </a>
      <a href="https://www.linkedin.com/in/larissa-temoteo/">
        <img src="https://custom-icon-badges.demolab.com/badge/LinkedIn-0A66C2?logo=linkedin-white&logoColor=fff" alt="LinkedIn" height="25"/>
      </a>
    </td>
    <td align="center">
      <a href="https://www.linkedin.com/in/lucas-nunes-matheus/">
        <img src="https://media.licdn.com/dms/image/v2/D4D03AQHN4SR2WsAIdA/profile-displayphoto-shrink_200_200/profile-displayphoto-shrink_200_200/0/1710163486566?e=1750896000&v=beta&t=o-9q_kscwkEexlcm92Cobx197j0MsiztrpiTQgiJ9Kg" width="100px;" alt="Foto de Lucas Matheus Nunes" style="border-radius:50%"/>
        <br />
        <b>Lucas Matheus Nunes</b>
      </a>
      <br />
      <a href="https://github.com/lucas-nunes-matheus">
        <img src="https://img.shields.io/badge/GitHub-%23121011.svg?logo=github&logoColor=white)" alt="GitHub" height="25"/>
      </a>
      <a href="https://www.linkedin.com/in/lucas-nunes-matheus/">
        <img src="https://custom-icon-badges.demolab.com/badge/LinkedIn-0A66C2?logo=linkedin-white&logoColor=fff" alt="LinkedIn" height="25"/>
      </a>
    </td>
    <td align="center">
      <a href="https://www.linkedin.com/in/rafael-furtado-b30715265/">
        <img src="https://media.licdn.com/dms/image/v2/D4D03AQGfsjSmMmtAsw/profile-displayphoto-shrink_200_200/profile-displayphoto-shrink_200_200/0/1709258479259?e=1750896000&v=beta&t=oIehlqkG2dGqrV8ya_JukCvuBgTEs-q7i32Oen49fdQ" width="100px;" alt="Foto de Rafael Furtado" style="border-radius:50%"/>
        <br />
        <b>Rafael Furtado</b>
      </a>
      <br />
      <a href="https://github.com/Rafaelfurtadovs">
        <img src="https://img.shields.io/badge/GitHub-%23121011.svg?logo=github&logoColor=white)" alt="GitHub" height="25"/>
      </a>
      <a href="https://www.linkedin.com/in/rafael-furtado-b30715265/">
        <img src="https://custom-icon-badges.demolab.com/badge/LinkedIn-0A66C2?logo=linkedin-white&logoColor=fff" alt="LinkedIn" height="25"/>
      </a>
    </td>
  </tr>
  <tr>
    <td align="center">
      <a href="https://www.linkedin.com/in/ryan-botelho-gartlan/">
        <img src="https://media.licdn.com/dms/image/v2/D5603AQGy5KTEKUM2pA/profile-displayphoto-shrink_200_200/profile-displayphoto-shrink_200_200/0/1728396516687?e=1750896000&v=beta&t=LjSCsFve87n2F4J7v-LzbwHHytG4SJnTxTigdBhVlUU" width="100px;" alt="Foto de Ryan Gartlan" style="border-radius:50%"/>
        <br />
        <b>Ryan Gartlan</b>
      </a>
      <br />
      <a href="https://github.com/ryanbotgar">
        <img src="https://img.shields.io/badge/GitHub-%23121011.svg?logo=github&logoColor=white)" alt="GitHub" height="25"/>
      </a>
      <a href="https://www.linkedin.com/in/ryan-botelho-gartlan/">
        <img src="https://custom-icon-badges.demolab.com/badge/LinkedIn-0A66C2?logo=linkedin-white&logoColor=fff" alt="LinkedIn" height="25"/>
      </a>
    </td>
    <td align="center">
      <a href="https://www.linkedin.com/in/tainacortez/">
        <img src="https://media.licdn.com/dms/image/v2/D4D03AQHV4IOZvu7n3A/profile-displayphoto-shrink_200_200/profile-displayphoto-shrink_200_200/0/1713277689597?e=1750896000&v=beta&t=fgvCFEght43z8dmT0PhJgj1Lg5VtXoCjZie0pNPujxA" width="100px;" alt="Foto de Tainá Cortez" style="border-radius:50%"/>
        <br />
        <b>Tainá Cortez</b>
      </a>
      <br />
      <a href="https://github.com/taicortezz">
        <img src="https://img.shields.io/badge/GitHub-%23121011.svg?logo=github&logoColor=white)" alt="GitHub" height="25"/>
      </a>
      <a href="https://www.linkedin.com/in/tainacortez/">
        <img src="https://custom-icon-badges.demolab.com/badge/LinkedIn-0A66C2?logo=linkedin-white&logoColor=fff" alt="LinkedIn" height="25"/>
      </a>
    </td>
    <td align="center">
      <a href="https://www.linkedin.com/in/thiagogomesalmeida/">
        <img src="https://media.licdn.com/dms/image/v2/D4D03AQHh3rHCD36uKA/profile-displayphoto-shrink_200_200/profile-displayphoto-shrink_200_200/0/1711828725384?e=1750896000&v=beta&t=Kggq5QNqIQ66GqL7_dT37fq5YO3NQAGBwX9BF0Fq8oU" width="100px;" alt="Foto de Thiago Gomes" style="border-radius:50%"/>
        <br />
        <b>Thiago Gomes</b>
      </a>
      <br />
      <a href="https://github.com/thiagomes07">
        <img src="https://img.shields.io/badge/GitHub-%23121011.svg?logo=github&logoColor=white)" alt="GitHub" height="25"/>
      </a>
      <a href="https://www.linkedin.com/in/thiagogomesalmeida/">
        <img src="https://custom-icon-badges.demolab.com/badge/LinkedIn-0A66C2?logo=linkedin-white&logoColor=fff" alt="LinkedIn" height="25"/>
      </a>
    </td>
  </tr>
  <tr>
    <td colspan="3" align="center">
      <a href="https://www.linkedin.com/in/viniciussavian/">
        <img src="https://media.licdn.com/dms/image/v2/D4E03AQFsD6PLB2Du0w/profile-displayphoto-shrink_200_200/profile-displayphoto-shrink_200_200/0/1710417108475?e=1750896000&v=beta&t=Qbzx-PMJMrTRJlEtE_NYRwVfMfOyY7Nf-duVxKugTmk" width="100px;" alt="Foto de Vinicius Savian" style="border-radius:50%"/>
        <br />
        <b>Vinicius Savian</b>
      </a>
      <br />
      <a href="https://github.com/ViniciusSavian">
        <img src="https://img.shields.io/badge/GitHub-%23121011.svg?logo=github&logoColor=white)" alt="GitHub" height="25"/>
      </a>
      <a href="https://www.linkedin.com/in/viniciussavian/">
        <img src="https://custom-icon-badges.demolab.com/badge/LinkedIn-0A66C2?logo=linkedin-white&logoColor=fff" alt="LinkedIn" height="25"/>
      </a>
    </td>
  </tr>
</table>
</div>

# Sumário

- [1. Introdução](#1-introdução)
- [2. Avaliação das Sprints](#2-avaliação-das-sprints)
  - [2.1 Sprint 1](#21-sprint-1)
  - [2.2 Sprint 2](#21-sprint-2)
  - [2.3 Sprint 3](#21-sprint-3)
  - [2.4 Sprint 4](#21-sprint-4)
  - [2.5 Sprint 5](#21-sprint-5)
- [3. Análise de Riscos](#3-análise-de-riscos)
  - [3.1 Riscos identificados](#31-riscos-identificados)
  - [3.2 Mitigação de Riscos](#32-mitigação-de-riscos)
- [4. Análise Post Mortem](#4-análise-post-mortem)
  - [4.1 Sucessos do Projeto](#41-sucessos-do-projeto)
  - [4.2 Oportunidades de Melhoria](#42-oportunidades-de-melhoria)
  - [4.3 Lições Aprendidas](#43-lições-aprendidas)

# 1. Introdução

&emsp; Este documento tem como objetivo apresentar uma análise abrangente do progresso e da execução do projeto ao longo de suas sprints, com foco na avaliação do desempenho da equipe, planejamento das próximas etapas e identificação de riscos e lições aprendidas. O conteúdo aqui disposto oferece uma visão detalhada da evolução do trabalho desde a Sprint 1 até a Sprint 5.

# 2. Avaliação das Sprints

&emsp; Nesta seção, é apresentada a análise detalhada do desempenho da equipe em cada uma das sprints do projeto. A avaliação contempla os principais acontecimentos, dificuldades enfrentadas, decisões tomadas e os resultados obtidos, com o objetivo de registrar a evolução do trabalho e identificar oportunidades de melhoria contínua ao longo do desenvolvimento.

## 2.1 Sprint 1

&emsp; A Sprint 1 foi marcada por um início promissor, com forte integração e comprometimento entre os membros da equipe. Desde o início, houve uma colaboração eficaz e uma comunicação fluida, fatores que contribuíram para um bom ambiente de trabalho e para o alinhamento inicial em relação aos objetivos do projeto.

&emsp; No entanto, a sprint apresentou desafios significativos relacionados à sua curta duração. A primeira semana contou com um feriado, reduzindo um dia útil, enquanto a segunda semana teve dois dias a menos, comprometendo ainda mais o tempo disponível. Além disso, como é comum em inícios de projeto, os primeiros dias foram pouco produtivos porque ainda não havíamos passado pelo kickoff, o que reduziu ainda mais o tempo efetivo de produção.

&emsp; Apesar dessas limitações, a equipe demonstrou capacidade de adaptação e respondeu bem ao cenário, ajustando seu planejamento de forma estratégica para manter a produtividade. O principal obstáculo, portanto, não esteve relacionado ao desempenho da equipe, mas sim às restrições temporais impostas pelo calendário e pelo momento inicial do projeto.

&emsp; Outro ponto crítico enfrentado foi o atraso no envio dos datasets pela empresa parceira, o que impactou diretamente na entrega de um dos artefatos previstos. Essa pendência gerou um efeito cascata que será sentido na Sprint 2, com aumento da demanda e necessidade de replanejamento para evitar sobrecargas.

&emsp; Em síntese, a Sprint 1 cumpriu seu papel de estabelecer as bases do projeto e foi fundamental para fortalecer a coesão do grupo. Apesar dos contratempos, a equipe mostrou maturidade e compromisso, o que gera confiança para as próximas etapas, especialmente considerando que as sprints futuras terão maior disponibilidade de tempo útil.

## 2.2 Sprint 2

### Pontos Fortes

&ensp;A Sprint 2 foi muito produtiva no geral, marcada por grande comprometimento da equipe, que se dedicou muito às entregas e também buscou ativamente o apoio das professoras sempre que surgiam dúvidas ou necessidades de validação. Essa postura de consultar os professores constantemente garantiu a qualidade das entregas e ajudou a manter o trabalho alinhado com as expectativas acadêmicas. Além disso, no início da sprint, realizamos discussões sobre o projeto, como sobre a modelagem, fluxo do usuário e etc, para garantir que todo o grupo compreendesse o escopo do projeto e as expectativas sobre o produto final. Esse momento de alinhamento deixou tudo mais claro para todos e facilitou muito a realização das tasks ao longo das duas semanas. Por fim, o grupo estava bem colaborativo, com todos trocando insights e ajudando uns aos outros quando era preciso.

### Pontos Fracos

&emsp;Embora tenhamos mantido o ritmo de trabalho, a má gestão do Trello dificultou a visualização do progresso das atividades, pois o quadro ficou desatualizado em vários momentos. A demora na abertura de pull requests provocou sobrecarga no último dia da sprint, forçando o time a revisar código com pressa e comprometendo a qualidade das revisões. Também sentimos falta de visibilidade sobre o andamento das tasks, já que alguns membros não compartilhavam atualizações de como estavam, o que deixou o grupo sem uma visão geral das entregas da Sprint 2. Por fim, embora as dailys tenham acontecido, seu uso não foi totalmente efetivo para expor impedimentos e tornar o fluxo de trabalho mais transparente; em alguns casos, as reuniões tornaram-se longas, sem foco nas entregas e dificuldades reais.

### Ações de Melhoria 

&emsp;Para a próxima sprint, iremos reforçar a atualização do Trello, fazendo com que cada integrante atualize suas tasks diariamente, garantindo a visibilidade do progresso de todos os membros. No que diz respeito aos pull requests, combinamos nesta sprint retrospective o comprometimento de todos os integrantes de abrir PR assim que finalizarem as tasks, para termos mais tempo para as revisões. Outro ponto de melhoria identificado foi começar as dailys pontualmente, para que cada membro apresente o que fez, o que está em andamento, aproveitando esse momento também para expor dificuldades e pedir ajuda. Quanto à comunicação interna, percebemos que talvez nem todo mundo se sinta 100% à vontade para expor suas dificuldades e, por isso, vamos trabalhar para manter um ambiente mais confortável, onde todos sintam liberdade para dizer quando estão com alguma dúvida ou travados em alguma parte.


## 2.3 Sprint 3

### Pontos Fortes
&emsp;A Sprint 3 apresentou avanços em relação às sprints anteriores, como no planejamento e gerenciamento das tasks. O uso do Trello foi melhor, com cards mais organizados, o que facilitou o acompanhamento do progresso das atividades. Além disso, houve uma melhora na frequência de abertura de pull requests, evitando a concentração de revisões no último dia da sprint e garantindo maior qualidade nas entregas.

&emsp;A comunicação interna também foi um ponto positivo, com atualizações mais detalhadas sobre o andamento de cada task, permitindo maior transparência e alinhamento entre os membros da equipe. No aspecto técnico, o grupo demonstrou alta produtividade no desenvolvimento dos serviços da solução, mesmo sendo o primeiro contato com tecnologias como React Native e .NET C#, o que reflete um rápido aprendizado.

&emsp;Outro ponto forte foi a melhoria na pontualidade das dailys e reuniões, além de uma maior padronização na criação de branches e commits, contribuindo para um fluxo de trabalho mais organizado.

### Pontos Fracos

&emsp;Apesar das melhorias no uso do Trello, ainda identificamos ineficiências, principalmente na definição prévia das tasks e na estimativa do tamanho delas antes do início da sprint. A falta de uma visão clara do escopo antes do planejamento dificultou a distribuição equilibrada de atividades.

&emsp;Outro ponto fraco foi a redução na gestão do conhecimento, com poucas discussões técnicas e alinhamentos essenciais entre as tasks. Isso impactou diretamente a entrega de algumas funcionalidades, como por exemplo, as regras de negócio do banco de dados, que dependiam de um alinhamento prévio com as regras dos serviços do backend. A falta desse alinhamento gerou atrasos e retrabalho.

&emsp;Além disso, observamos uma falta de engajamento coletivo nas atividades de sala de aula, sejam ponderadas ou não. A participação do grupo foi abaixo do esperado, o que pode ter limitado o aproveitamento das orientações e discussões propostas pelos professores.

### Ações de Melhoria

&emsp;Para a próxima sprint, priorizaremos um planejamento mais detalhado antes do início das atividades, garantindo que as tasks sejam bem definidas e estimadas com antecedência, evitando sobrecargas no escopo. Também fortaleceremos as discussões técnicas, promovendo reuniões de alinhamento frequentes para garantir que todas as dependências entre as tasks sejam mapeadas e resolvidas no tempo certo.

&emsp;Quanto ao engajamento, incentivaremos uma participação mais ativa de todos os membros nas aulas e atividades propostas, reconhecendo a importância desses momentos para o desenvolvimento do projeto. Por fim, manteremos as melhorias já implementadas na comunicação e no versionamento de código, buscando consolidar um fluxo de trabalho cada vez melhor.

## 2.4 Sprint 4

### Pontos Fortes

&emsp;A Sprint 4 demonstrou a capacidade de adaptação e resiliência da equipe diante dos desafios técnicos e operacionais que surgiram durante o ciclo. A comunicação entre os membros se manteve como um diferencial, dando continuidade ao ponto forte identificado na Sprint 3, com atualizações claras sobre o andamento das atividades e transparência no compartilhamento de informações.

&emsp;Um avanço significativo em relação à Sprint 3 foi a recuperação das discussões técnicas e da gestão do conhecimento. O grupo conseguiu promover mais alinhamentos entre as tasks e discussões colaborativas para construção de soluções, corrigindo o ponto fraco que havia sido identificado na sprint anterior. Essas sessões de discussão se mostraram fundamentais para resolver problemas de forma coletiva e eficiente.

&emsp;A equipe também demonstrou boa capacidade de resolução de problemas durante a sprint, conseguindo lidar adequadamente com os impedimentos que surgiram ao longo do ciclo. Isso reflete uma maturidade crescente no processo de desenvolvimento e na gestão de crises técnicas.

### Pontos Fracos

&emsp;A gestão do Trello apresentou uma regressão significativa em relação aos avanços conquistados na Sprint 3. Enquanto na sprint anterior tínhamos conseguido melhorar a organização dos cards e o acompanhamento das atividades, na Sprint 4 houve uma perda nesse processo. O grupo não conseguiu manter a granularidade adequada dos cards no Kanban e o acompanhamento das tasks ficou prejudicado, dificultando a visibilidade do progresso real das atividades.

&emsp;Outro problema grave foi o retrocesso na frequência de abertura de pull requests, contradizendo diretamente a melhoria que havia sido alcançada na Sprint 3. Enquanto na sprint anterior conseguimos evitar a concentração de revisões no último dia, na Sprint 4 voltamos ao padrão problemático de acumular PRs no final do ciclo, comprometendo a qualidade das entregas e gerando pressão desnecessária sobre a equipe.

&emsp;O desempenho da equipe apresentou desequilíbrio entre as semanas da sprint, com produtividade inconsistente que impactou negativamente o fluxo de trabalho. Além disso, identificamos problemas significativos com dependências entre tasks, onde algumas atividades acabaram bloqueando ou comprometendo a entrega de outras. Esse problema agravou a dificuldade de planejamento e estimativa das tasks que já havia sido identificada na Sprint 3, evidenciando que as ações de melhoria propostas anteriormente não foram adequadamente implementadas.

### Ações de Melhoria

&emsp;Para a próxima sprint, será fundamental retomar as práticas que funcionaram bem na Sprint 3, especialmente no que se refere à gestão do Trello e à frequência de abertura de pull requests. Estabeleceremos checkpoints diários específicos para garantir que os cards sejam atualizados adequadamente e que os PRs sejam distribuídos ao longo da sprint, evitando o acúmulo no final do ciclo.

&emsp;Implementaremos um processo mais rigoroso de mapeamento de dependências entre tasks durante a fase de planejamento da sprint. Isso incluirá a criação de um fluxo visual das interdependências e a definição clara de predecessores e sucessores para cada atividade, garantindo que as tasks sejam sequenciadas adequadamente e que não haja bloqueios desnecessários.

&emsp;Para resolver o problema de produtividade desequilibrada, estabeleceremos marcos intermediários com revisões no meio da sprint, permitindo avaliar o progresso e redistribuir esforços conforme necessário. Isso ajudará a manter um ritmo mais consistente ao longo de todo o ciclo.

&emsp;Manteremos e consolidaremos as melhorias já alcançadas na comunicação e nas discussões técnicas, que se mostraram eficazes na Sprint 4. Continuaremos promovendo reuniões de alinhamento técnico sempre que houver interdependências críticas entre as atividades, fortalecendo ainda mais a gestão do conhecimento da equipe.

## 2.5 Sprint 5

_conteúdo_
**Nota:** Insira informações sobre mudanças realizadas.

# 3. Análise de Riscos

_conteúdo_

## 3.1 Riscos identificados

_conteúdo_

## 3.2 Mitigação de Riscos

_conteúdo_

# 4. Análise Post Mortem

_conteúdo_

## 4.1 Sucessos do Projeto

_conteúdo_

## 4.2 Oportunidades de Melhoria

_conteúdo_

## 4.3 Lições Aprendidas

_conteúdo_
