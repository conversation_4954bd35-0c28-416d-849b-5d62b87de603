using Pati.ClassificationService.Application.Services;
using Pati.ClassificationService.DTOs;
using Pati.ClassificationService.Infrastructure.Data;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace Pati.ClassificationService.Infrastructure.Services
{
    /// <summary>
    /// Implementação do serviço de classificação de carteiras
    /// Implementa as regras CVM 175 e integração com modelo SVD
    /// </summary>
    public class ClassificationService : IClassificationService
    {
        private readonly ClassificationDbContext _context;
        private readonly ISVDModelService _svdModelService;
        private readonly IRecommendationServiceClient _recommendationClient;
        private readonly ILogger<ClassificationService> _logger;

        public ClassificationService(
            ClassificationDbContext context,
            ISVDModelService svdModelService,
            IRecommendationServiceClient recommendationClient,
            ILogger<ClassificationService> logger)
        {
            _context = context;
            _svdModelService = svdModelService;
            _recommendationClient = recommendationClient;
            _logger = logger;
        }

        /// <summary>
        /// Classifica uma carteira individual identificando inconformidades
        /// </summary>
        public async Task<PortfolioOutputDto> ClassifyPortfolioAsync(PortfolioInputDto portfolioInput)
        {
            Console.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] 🎯 INÍCIO: Classificação individual - Cliente {portfolioInput.AccountId}, Carteira {portfolioInput.PortfolioId}");
            _logger.LogInformation("Starting portfolio classification for account {AccountId}, portfolio {PortfolioId}",
                portfolioInput.AccountId, portfolioInput.PortfolioId);

            try
            {
                // 1. Buscar cliente no banco
                Console.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] 🔍 BUSCANDO: Cliente {portfolioInput.AccountId} no banco de dados");
                var client = await _context.Clients.FindAsync(portfolioInput.AccountId);
                if (client == null)
                {
                    Console.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] ❌ NÃO ENCONTRADO: Cliente {portfolioInput.AccountId} não encontrado no banco");
                    throw new ArgumentException($"Client {portfolioInput.AccountId} not found");
                }
                Console.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] ✅ ENCONTRADO: Cliente {portfolioInput.AccountId} - {client.Name}");

                // 2. Análise SVD simulada
                Console.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] 🤖 SVD: Executando análise SVD simulada");
                var svdConfidence = 0.729m;
                var predictedProfile = "Moderado";
                Console.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] 🤖 SVD: Perfil previsto = {predictedProfile}, Confiança = {svdConfidence}");

                // 3. Detectar inconformidades
                Console.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] 🔍 INCONFORMIDADES: Analisando {portfolioInput.Assets.Count} ativos para inconformidades");
                var inconsistencies = new List<InconsistencyDto>();
                foreach (var asset in portfolioInput.Assets)
                {
                    if (!IsRiskAllowedForProfile(portfolioInput.PortfolioProfile, asset.InvestmentProfile))
                    {
                        Console.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] ⚠️  INCONFORMIDADE: Ativo {asset.AssetId} ({asset.InvestmentProfile}) incompatível com perfil {portfolioInput.PortfolioProfile}");
                        inconsistencies.Add(new InconsistencyDto
                        {
                            AssetId = asset.AssetId,
                            Name = asset.Name,
                            Description = $"Ativo {asset.InvestmentProfile} incompatível com perfil {portfolioInput.PortfolioProfile}",
                            Severity = "high"
                        });
                    }
                }
                Console.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] 🔍 INCONFORMIDADES: {inconsistencies.Count} inconformidades detectadas");

                // 4. Calcular alocação
                Console.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] 📊 ALOÇÃO: Calculando alocação de renda fixa vs variável");
                var totalValue = portfolioInput.Assets.Sum(a => a.Value);
                var fixedIncomeValue = portfolioInput.Assets
                    .Where(a => a.IncomeType == "Renda Fixa")
                    .Sum(a => a.Value);
                
                var fixedIncomeRatio = totalValue > 0 ? fixedIncomeValue / totalValue : 1.0m;
                Console.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] 📊 ALOÇÃO: Renda Fixa = {fixedIncomeRatio:P1}, Renda Variável = {(1.0m - fixedIncomeRatio):P1}");

                // 5. Determinar compliance
                Console.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] ✅ COMPLIANCE: Determinando status de conformidade");
                var hasHighSeverityIssues = inconsistencies.Any(i => i.Severity == "high");
                var isCompliant = !hasHighSeverityIssues;
                Console.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] ✅ COMPLIANCE: Cliente {portfolioInput.AccountId} - Conforme = {isCompliant}");

                // 6. Atualizar cliente no banco
                Console.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] 💾 SALVANDO: Atualizando perfil de risco da carteira no PostgreSQL");
                await UpdateClientAsync(portfolioInput.AccountId, predictedProfile, isCompliant);
                Console.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] 💾 SALVO: Perfil de risco atualizado para '{predictedProfile}'");

                // 7. Criar resultado
                var result = new PortfolioOutputDto
                {
                    AccountId = portfolioInput.AccountId,
                    PortfolioId = portfolioInput.PortfolioId,
                    PortfolioProfile = predictedProfile,
                    Inconsistencies = inconsistencies,
                    IncomeAllocation = new IncomeAllocationDto
                    {
                        FixedIncome = fixedIncomeRatio,
                        VariableIncome = 1.0m - fixedIncomeRatio
                    }
                };

                Console.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] ✅ FINALIZADO: Classificação individual concluída - Cliente {portfolioInput.AccountId}, {inconsistencies.Count} inconsistências");
                _logger.LogInformation("Portfolio classification completed for account {AccountId}. Found {Count} inconsistencies",
                    portfolioInput.AccountId, inconsistencies.Count);

                return result;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] ❌ ERRO: Falha na classificação individual - Cliente {portfolioInput.AccountId} - {ex.Message}");
                _logger.LogError(ex, "Error classifying portfolio for account {AccountId}", portfolioInput.AccountId);
                throw;
            }
        }

        /// <summary>
        /// Classifica todas as carteiras do sistema em lote
        /// </summary>
        public async Task<object> ClassifyAllPortfoliosAsync()
        {
            Console.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] 🔍 INÍCIO: Iniciando classificação em lote de todas as carteiras");
            Console.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] 📋 NOTA: RecommendationService será chamado apenas uma vez no final do processamento");
            _logger.LogInformation("Starting batch portfolio classification");

            var startTime = DateTime.UtcNow;
            var processedCount = 0;
            var skippedCount = 0;
            var totalInconsistencies = 0;

            try
            {
                // Buscar todos os clientes
                Console.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] 📊 BUSCA: Buscando todos os clientes no PostgreSQL");
                var clients = await _context.Clients.ToListAsync();
                Console.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] 📊 ENCONTRADOS: {clients.Count} clientes encontrados no banco");

                foreach (var client in clients)
                {
                    try
                    {
                        Console.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] 👤 PROCESSANDO: Cliente ID {client.ClientId} - {client.Name}");
                        
                        // Buscar investimentos do cliente
                        Console.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] 💰 BUSCANDO: Investimentos do cliente {client.ClientId}");
                        var clientInvestments = await _context.ClientInvestments
                            .Where(ci => ci.ClientId == client.ClientId)
                            .ToListAsync();
                        Console.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] 💰 ENCONTRADOS: {clientInvestments.Count} investimentos para cliente {client.ClientId}");

                        // Se não há investimentos, pular este cliente
                        if (!clientInvestments.Any())
                        {
                            Console.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] ⚠️  PULANDO: Cliente {client.ClientId} não tem investimentos - pulando classificação");
                            skippedCount++;
                            continue; // Pular para o próximo cliente
                        }

                        // Criar portfolio com investimentos reais
                        var assets = clientInvestments.Select(ci => new AssetInputDto
                        {
                            AssetId = $"asset_{ci.InvestmentId}",
                            Name = $"Investment {ci.InvestmentId}",
                            Type = "bond",
                            IncomeType = "Renda Fixa",
                            InvestmentProfile = "Moderado",
                            Quantity = ci.Quantity,
                            Value = ci.InvestedAmount
                        }).ToList();

                        var portfolioInput = new PortfolioInputDto
                        {
                            AccountId = client.ClientId,
                            PortfolioId = $"portfolio_{client.ClientId}",
                            PortfolioProfile = client.RiskProfileForm ?? "Moderado",
                            Assets = assets
                        };

                        Console.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] 🎯 CLASSIFICANDO: Iniciando classificação da carteira do cliente {client.ClientId}");
                        
                        // Processar carteira
                        var result = await ClassifyPortfolioAsync(portfolioInput);
                        totalInconsistencies += result.Inconsistencies.Count;
                        processedCount++;
                        
                        Console.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] ✅ CONCLUÍDO: Cliente {client.ClientId} processado - {result.Inconsistencies.Count} inconsistências encontradas");
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] ❌ ERRO: Falha ao processar cliente {client.ClientId} - {ex.Message}");
                        _logger.LogError(ex, "Error processing client {ClientId} in batch classification", client.ClientId);
                    }
                }

                var duration = DateTime.UtcNow - startTime;

                Console.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] 🎉 FINALIZADO: Classificação em lote concluída - {processedCount} clientes processados, {skippedCount} clientes pulados, {totalInconsistencies} inconsistências totais em {duration.TotalMilliseconds:F0}ms");
                _logger.LogInformation("Batch portfolio classification completed for {Count} clients", processedCount);

                // Chamar serviço de recomendação apenas uma vez no final
                Console.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] 📤 RECOMENDAÇÃO: Iniciando geração de recomendações para carteiras inconformes");
                try
                {
                    // Chamar o endpoint sem enviar dados - ele busca tudo no PostgreSQL
                    var success = await _recommendationClient.SendPortfolioAnalysisAsync(null);
                    
                    if (success)
                    {
                        Console.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] ✅ RECOMENDAÇÃO: Sinal enviado com sucesso para o serviço de recomendação");
                    }
                    else
                    {
                        Console.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] ⚠️  RECOMENDAÇÃO: Falha ao enviar sinal para o serviço de recomendação");
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] ⚠️  FALHA RECOMENDAÇÃO: Erro ao chamar serviço de recomendação - {ex.Message}");
                    _logger.LogWarning(ex, "Failed to call recommendation service after batch classification");
                }

                return new
                {
                    message = "Batch classification completed successfully",
                    timestamp = DateTime.UtcNow,
                    processedClients = processedCount,
                    skippedClients = skippedCount,
                    totalInconsistencies = totalInconsistencies,
                    executionTimeMs = duration.TotalMilliseconds
                };
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] 💥 ERRO CRÍTICO: Falha na classificação em lote - {ex.Message}");
                _logger.LogError(ex, "Error in batch portfolio classification");
                throw;
            }
        }

        /// <summary>
        /// Verifica se um risco é permitido para um perfil específico
        /// </summary>
        public bool IsRiskAllowedForProfile(string profile, string risk)
        {
            var riskRules = new Dictionary<string, string[]>
            {
                { "Conservador", new[] { "Conservador" } },
                { "Moderado", new[] { "Conservador", "Moderado" } },
                { "Arrojado", new[] { "Conservador", "Moderado", "Arrojado" } },
                { "Sofisticado", new[] { "Conservador", "Moderado", "Arrojado", "Sofisticado" } }
            };

            return riskRules.ContainsKey(profile) && riskRules[profile].Contains(risk);
        }

        /// <summary>
        /// Obtém as proporções ideais de Renda Fixa e Renda Variável para um perfil
        /// </summary>
        public (decimal FixedIncome, decimal VariableIncome) GetIdealProportions(string profile)
        {
            var idealProportions = new Dictionary<string, (decimal FixedIncome, decimal VariableIncome)>
            {
                { "Conservador", (0.9m, 0.1m) },
                { "Moderado", (0.6m, 0.4m) },
                { "Arrojado", (0.3m, 0.7m) },
                { "Sofisticado", (0.2m, 0.8m) }
            };

            return idealProportions.GetValueOrDefault(profile, (0.6m, 0.4m));
        }

        private async Task UpdateClientAsync(int clientId, string riskProfile, bool isCompliant)
        {
            try
            {
                Console.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] 💾 UPDATE: Buscando cliente {clientId} para atualização");
                var client = await _context.Clients.FindAsync(clientId);
                if (client == null) 
                {
                    Console.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] ❌ UPDATE: Cliente {clientId} não encontrado para atualização");
                    return;
                }

                var previousCompliance = !client.NonCompliance;
                var previousProfile = client.RiskProfileWallet;

                Console.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] 💾 UPDATE: Cliente {clientId} - Perfil anterior: '{previousProfile}' -> Novo: '{riskProfile}'");
                Console.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] 💾 UPDATE: Cliente {clientId} - Compliance anterior: {previousCompliance} -> Novo: {isCompliant}");

                client.RiskProfileWallet = riskProfile;
                client.NonCompliance = !isCompliant;

                if (previousCompliance != isCompliant)
                {
                    Console.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] 📝 HISTÓRICO: Mudança de compliance detectada - criando registro no histórico");
                    var history = new Domain.ComplianceHistory(clientId, previousCompliance, isCompliant);
                    _context.ComplianceHistory.Add(history);
                }

                Console.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] 💾 SAVE: Salvando alterações no PostgreSQL");
                await _context.SaveChangesAsync();
                Console.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] 💾 SAVED: Alterações salvas com sucesso no PostgreSQL");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] ❌ UPDATE ERRO: Falha ao atualizar cliente {clientId} - {ex.Message}");
                _logger.LogError(ex, "Error updating client {ClientId}", clientId);
            }
        }
    }
}
