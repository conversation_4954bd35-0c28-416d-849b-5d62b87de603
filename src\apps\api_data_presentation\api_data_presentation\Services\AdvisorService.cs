using api_data_presentation.Models.DTOs;
using api_data_presentation.Models.QueryParameters;
using api_data_presentation.Repositories.Interfaces;
using api_data_presentation.Services.Interfaces;

namespace api_data_presentation.Services
{
    public class AdvisorService : IAdvisorService
    {
        private readonly IClientRepository _clientRepository;
        private readonly IRecommendationRepository _recommendationRepository;

        public AdvisorService(IClientRepository clientRepository, IRecommendationRepository recommendationRepository)
        {
            _clientRepository = clientRepository;
            _recommendationRepository = recommendationRepository;
        }

        public async Task<ComplianceStatsDto> GetComplianceStatsAsync(int advisorId)
        {
            if (advisorId <= 0)
                throw new ArgumentException("Advisor ID deve ser maior que zero.");

            try
            {
                var complianceStats = await _clientRepository.GetComplianceStatsByAdvisorIdAsync(advisorId);
                return new ComplianceStatsDto
                {
                    Conform = complianceStats.Conform,
                    Inconform = complianceStats.Inconform
                };
            }
            catch
            {
                return new ComplianceStatsDto
                {
                    Conform = 0,
                    Inconform = 0
                };
            }
        }

        public async Task<List<ClientListDto>> GetClientsAsync(int advisorId, ClientListQueryParams queryParams)
        {
            if (advisorId <= 0)
                throw new ArgumentException("AdvisorId deve ser maior que zero.");

            if (queryParams == null)
                throw new ArgumentNullException(nameof(queryParams), "Parâmetros de consulta não podem ser nulos.");

            if (!queryParams.IsValidOrderBy())
                throw new ArgumentException("Parâmetro de ordenação inválido.");

            var clients = await _clientRepository.GetClientsByAdvisorIdAsync(advisorId, queryParams);

            return clients.Select(clientTuple => new ClientListDto
            {
                ClientId = clientTuple.Client.ClientId,
                Name = clientTuple.Client.Name,
                RiskProfileForm = clientTuple.Client.RiskProfileForm,
                ComplianceStatus = clientTuple.Client.Compliance ? "Conforme" : "Inconforme",
                TotalInvested = clientTuple.TotalInvested
            }).ToList();
        }

        public async Task<AdvisorAuthResponseDto> AuthenticateOrCreateAsync(string uid)
        {
            if (string.IsNullOrWhiteSpace(uid))
                throw new ArgumentException("O UID não pode ser nulo ou vazio.");

            var existingAdvisor = await _clientRepository.GetAdvisorByUidAsync(uid);
            if (existingAdvisor != null)
            {
                return new AdvisorAuthResponseDto
                {
                    AdvisorId = existingAdvisor.AdvisorId
                };
            }

            var newAdvisorId = await _clientRepository.CreateAdvisorAsync(uid);
            return new AdvisorAuthResponseDto
            {
                AdvisorId = newAdvisorId
            };
        }

        public async Task<List<ClientInvestmentDto>> GetClientInvestmentsAsync(int clientId, InvestmentQueryParams queryParams)
        {
            if (clientId <= 0)
                throw new ArgumentException("ClientId deve ser maior que zero.");

            if (queryParams == null)
                throw new ArgumentNullException(nameof(queryParams), "Parâmetros de consulta não podem ser nulos.");

            if (!queryParams.IsValidOrderBy())
                throw new ArgumentException("Parâmetro de ordenação inválido.");

            var investments = await _clientRepository.GetClientInvestmentsAsync(clientId, queryParams);

            return investments.Select(i => new ClientInvestmentDto
            {
                InvestmentType = i.Investment?.Type ?? string.Empty,
                InvestmentRisk = i.Investment?.Risk,
                Quantity = 1,
                InvestmentDate = i.InvestmentDate,
                TotalInvested = i.InvestedAmount,
                InvestmentName = i.Investment?.Name ?? string.Empty
            }).ToList();
        }

        public async Task<ClientRecommendationsDto?> GetClientRecommendationsAsync(int clientId)
        {
            if (clientId <= 0)
                throw new ArgumentException("ClientId deve ser maior que zero.");

            var client = await _clientRepository.GetClientByIdAsync(clientId);
            if (client == null)
                return null;

            var mongoRecommendations = await _recommendationRepository.GetRecommendationsByClientIdAsync(clientId.ToString());

            var recommendedInvestments = new List<RecommendedInvestmentDto>();
            if (mongoRecommendations?.Investments != null)
            {
                foreach (var investment in mongoRecommendations.Investments)
                {
                    var investmentDetails = await _clientRepository.GetInvestmentByIdAsync(int.Parse(investment.InvestmentId));
                    if (investmentDetails != null)
                    {
                        recommendedInvestments.Add(new RecommendedInvestmentDto
                        {
                            InvestmentId = investmentDetails.InvestmentId,
                            Name = investmentDetails.Name,
                            Type = investmentDetails.Type,
                            Risk = investmentDetails.Risk,
                            Score = investment.Score
                        });
                    }
                }
            }

            return new ClientRecommendationsDto
            {
                ClientId = client.ClientId,
                Email = client.Email,
                Phone = client.PhoneNumber,
                RiskProfileForm = client.RiskProfileForm,
                RiskProfileWallet = client.RiskProfileWallet,
                RecommendedInvestments = recommendedInvestments
            };
        }
    }
}