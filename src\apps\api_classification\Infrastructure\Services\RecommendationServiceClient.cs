using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Pati.ClassificationService.Application.Services;
using Pati.ClassificationService.DTOs;
using System.Net.Http;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;

namespace Pati.ClassificationService.Infrastructure.Services
{
    public class RecommendationServiceClient : IRecommendationServiceClient
    {
        private readonly HttpClient _httpClient;
        private readonly ILogger<RecommendationServiceClient> _logger;
        private readonly RecommendationServiceOptions _options;

        public RecommendationServiceClient(
            HttpClient httpClient,
            ILogger<RecommendationServiceClient> logger,
            IOptions<RecommendationServiceOptions> options)
        {
            _httpClient = httpClient ?? throw new ArgumentNullException(nameof(httpClient));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _options = options?.Value ?? throw new ArgumentNullException(nameof(options));
            
            // Log das configurações carregadas
            Console.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] 🔧 RECOMENDAÇÃO: Configurações carregadas:");
            Console.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] 🔧 RECOMENDAÇÃO: BaseUrl = {_options.BaseUrl}");
            Console.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] 🔧 RECOMENDAÇÃO: AnalysisEndpoint = {_options.AnalysisEndpoint}");
            Console.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] 🔧 RECOMENDAÇÃO: HealthCheckEndpoint = {_options.HealthCheckEndpoint}");
            Console.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] 🔧 RECOMENDAÇÃO: HttpClient.BaseAddress = {_httpClient.BaseAddress}");
        }

        public async Task<bool> SendPortfolioAnalysisAsync(PortfolioOutputDto? portfolioOutput)
        {
            try
            {
                // Construir URL completa
                var baseUrl = _httpClient.BaseAddress?.ToString() ?? "http://recommendation-service:80";
                var fullUrl = $"{baseUrl.TrimEnd('/')}{_options.AnalysisEndpoint}";
                
                if (portfolioOutput == null)
                {
                    // Chamada simples para iniciar recomendações sem dados
                    Console.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] 📤 RECOMENDAÇÃO: Iniciando geração de recomendações (sem dados)");
                    Console.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] 📤 RECOMENDAÇÃO: URL Completa: {fullUrl}");
                    
                    var responseSimple = await _httpClient.PostAsync(fullUrl, null);
                    
                    if (responseSimple.IsSuccessStatusCode)
                    {
                        Console.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] ✅ RECOMENDAÇÃO: Geração de recomendações iniciada com sucesso - Status: {responseSimple.StatusCode}");
                        return true;
                    }
                    else
                    {
                        var errorContent = await responseSimple.Content.ReadAsStringAsync();
                        Console.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] ❌ RECOMENDAÇÃO: Falha ao iniciar recomendações - Status: {responseSimple.StatusCode}, Conteúdo: {errorContent}");
                        return false;
                    }
                }

                Console.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] 📤 RECOMENDAÇÃO: Preparando envio para RecommendationService - Portfolio {portfolioOutput.PortfolioId}");
                Console.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] 📤 RECOMENDAÇÃO: Base URL: {baseUrl}");
                Console.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] 📤 RECOMENDAÇÃO: URL Completa: {fullUrl}");
                Console.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] 📤 RECOMENDAÇÃO: Cliente: {portfolioOutput.AccountId}, Inconsistências: {portfolioOutput.Inconsistencies?.Count ?? 0}");

                var json = JsonSerializer.Serialize(portfolioOutput, new JsonSerializerOptions
                {
                    PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
                    WriteIndented = true
                });

                var content = new StringContent(json, Encoding.UTF8, "application/json");

                Console.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] 📤 RECOMENDAÇÃO: Enviando requisição HTTP POST para RecommendationService");
                _logger.LogInformation("Sending portfolio analysis to RecommendationService for portfolio {PortfolioId}", 
                    portfolioOutput.PortfolioId);

                var response = await _httpClient.PostAsync(fullUrl, content);

                if (response.IsSuccessStatusCode)
                {
                    Console.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] ✅ RECOMENDAÇÃO: Requisição enviada com sucesso - Status: {response.StatusCode}");
                    _logger.LogInformation("Successfully sent portfolio analysis to RecommendationService for portfolio {PortfolioId}", 
                        portfolioOutput.PortfolioId);
                    return true;
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    Console.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] ❌ RECOMENDAÇÃO: Falha na requisição - Status: {response.StatusCode}, Conteúdo: {errorContent}");
                    _logger.LogError("Failed to send portfolio analysis to RecommendationService. Status: {StatusCode}, Content: {Content}", 
                        response.StatusCode, errorContent);
                    return false;
                }
            }
            catch (HttpRequestException ex)
            {
                Console.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] ❌ RECOMENDAÇÃO: Erro HTTP - {ex.Message}");
                _logger.LogError(ex, "HTTP error occurred while sending portfolio analysis to RecommendationService");
                return false;
            }
            catch (TaskCanceledException ex)
            {
                Console.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] ⏰ RECOMENDAÇÃO: Timeout na requisição - {ex.Message}");
                _logger.LogError(ex, "Timeout occurred while sending portfolio analysis to RecommendationService");
                return false;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] 💥 RECOMENDAÇÃO: Erro inesperado - {ex.Message}");
                _logger.LogError(ex, "Unexpected error occurred while sending portfolio analysis to RecommendationService");
                return false;
            }
        }

        public async Task<bool> IsServiceAvailableAsync()
        {
            try
            {
                Console.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] 🔍 DISPONIBILIDADE: Verificando disponibilidade do RecommendationService");
                _logger.LogDebug("Checking RecommendationService availability");

                // Construir URL completa
                var baseUrl = _httpClient.BaseAddress?.ToString() ?? "http://recommendation-service:80";
                var fullUrl = $"{baseUrl.TrimEnd('/')}{_options.HealthCheckEndpoint}";
                
                Console.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] 🔍 DISPONIBILIDADE: URL de health check: {fullUrl}");

                var response = await _httpClient.GetAsync(fullUrl);
                
                var isAvailable = response.IsSuccessStatusCode;
                
                Console.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] 🔍 DISPONIBILIDADE: RecommendationService disponível = {isAvailable} (Status: {response.StatusCode})");
                _logger.LogDebug("RecommendationService availability check result: {IsAvailable}", isAvailable);
                
                return isAvailable;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] ❌ DISPONIBILIDADE: RecommendationService não disponível - {ex.Message}");
                _logger.LogWarning(ex, "RecommendationService is not available");
                return false;
            }
        }
    }

    /// <summary>
    /// Configurações para o RecommendationService
    /// </summary>
    public class RecommendationServiceOptions
    {
        public const string SectionName = "RecommendationService";

        public string BaseUrl { get; set; } = "http://localhost:18502";
        public string AnalysisEndpoint { get; set; } = "/api/Recomendacao/iniciar";
        public string HealthCheckEndpoint { get; set; } = "/health";
        public int TimeoutSeconds { get; set; } = 30;
    }
}
